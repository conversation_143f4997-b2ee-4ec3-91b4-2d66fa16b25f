export declare function kebabCaseAlphanumeric(str: string): string;
/**
 * Return a safe variable name for a given string.
 */
export declare function safeVarName(str: string): string;
/**
 * Converts a string to a valid filename (NOT path), stripping out invalid characters.
 */
export declare function safeFilename(str: string): string;
/**
 * Removes import statements from the top of a file. Keeps import.meta and inline, async `import()`
 * calls.
 */
export declare function removeImportStatements(text: string): string;
/**
 * Removes imports, ensuring that some of WXT's client imports are present, so that entrypoints can be parsed if auto-imports are disabled.
 */
export declare function removeProjectImportStatements(text: string): string;
