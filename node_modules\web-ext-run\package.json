{"name": "web-ext-run", "version": "0.2.3", "description": "A tool to open and run web extensions", "type": "module", "main": "index.js", "exports": {".": "./index.js", "./util/adb": "./lib/util/adb.js", "./util/logger": "./lib/util/logger.js", "./util/submit-addon": "./lib/util/submit-addon.js"}, "files": ["index.js", "lib/**"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "engine-strict": true, "scripts": {"build": "node scripts/build", "start": "node scripts/develop", "test": "node scripts/test", "test-coverage": "node scripts/test --coverage", "test-functional": "node scripts/test-functional", "prettier": "prettier --write '**'", "prettier-ci": "prettier --list-different '**' || (echo '\n\nThis failure means you did not run `npm run prettier-dev` before committing\n\n' && exit 1)", "prettier-dev": "pretty-quick --branch master"}, "homepage": "https://github.com/aklinker1/web-ext-run", "repository": {"type": "git", "url": "git://github.com/aklinker1/web-ext-run.git"}, "bugs": {"url": "http://github.com/aklinker1/web-ext-run/issues"}, "keywords": ["web", "extensions", "web extensions", "browser extensions", "firefox", "mozilla", "add-ons", "google", "chrome", "opera"], "dependencies": {"@babel/runtime": "7.27.0", "@devicefarmer/adbkit": "3.3.8", "chrome-launcher": "1.1.2", "debounce": "1.2.1", "es6-error": "4.1.1", "firefox-profile": "4.7.0", "fx-runner": "1.4.0", "multimatch": "6.0.0", "node-notifier": "10.0.1", "parse-json": "7.1.1", "pino": "9.6.0", "promise-toolbox": "0.21.0", "set-value": "4.1.0", "source-map-support": "0.5.21", "strip-bom": "5.0.0", "strip-json-comments": "5.0.1", "tmp": "0.2.3", "update-notifier": "7.3.1", "watchpack": "2.4.2", "ws": "8.18.1", "zip-dir": "2.0.0"}, "devDependencies": {"@babel/cli": "7.27.0", "@babel/core": "7.26.10", "@babel/eslint-parser": "7.27.0", "@babel/preset-env": "7.26.9", "@babel/register": "7.25.9", "@commitlint/cli": "19.8.0", "@commitlint/config-conventional": "19.8.0", "babel-plugin-istanbul": "7.0.0", "babel-plugin-transform-inline-environment-variables": "0.4.4", "chai": "5.2.0", "chai-as-promised": "8.0.1", "copy-dir": "1.3.0", "crc-32": "1.2.2", "cross-env": "7.0.3", "deepcopy": "2.1.0", "eslint": "8.57.0", "eslint-plugin-async-await": "0.0.0", "eslint-plugin-import": "2.31.0", "fs-extra": "11.3.0", "git-rev-sync": "3.0.2", "html-entities": "2.6.0", "mocha": "11.1.0", "nyc": "17.1.0", "prettier": "3.5.3", "pretty-quick": "4.1.1", "prettyjson": "1.2.5", "shelljs": "0.8.5", "sinon": "20.0.0", "testdouble": "3.20.2", "yauzl": "2.10.0"}, "author": "<PERSON>", "license": "MPL-2.0", "nyc": {"include": "src/**/*.js", "reporter": ["lcov", "text"]}}