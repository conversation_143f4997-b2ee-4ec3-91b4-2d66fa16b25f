{"name": "@webext-core/fake-browser", "version": "1.3.2", "description": "An in-memory implementation of webextension-polyfill for testing. Supports all test frameworks (Vitest, Jest, etc)", "license": "MIT", "keywords": ["web-extension", "browser-extension", "chrome-extension", "webext", "web-ext", "chrome", "firefox", "safari", "browser", "extension", "testing", "vite", "jest", "mock", "fake", "webextension-polyfill"], "homepage": "https://github.com/aklinker1/webext-core/tree/main/packages/fake-browser", "repository": {"type": "git", "url": "https://github.com/aklinker1/webext-core", "directory": "packages/fake-browser"}, "type": "module", "publishConfig": {"access": "public"}, "files": ["lib"], "main": "lib/index.cjs", "module": "lib/index.js", "types": "lib/index.d.ts", "exports": {".": {"import": "./lib/index.js", "require": "./lib/index.cjs"}}, "scripts": {"build": "buildc -- bun gen && tsup src/index.ts --clean --out-dir lib --dts --format esm,cjs,iife --global-name webExtCoreFakeBrowser", "test": "buildc --deps-only -- vitest", "test:coverage": "buildc --deps-only -- vitest run --coverage", "check": "buildc --deps-only -- tsc --noEmit", "gen": "tsx scripts/generate-base.ts && prettier --write src/base.gen.ts"}, "devDependencies": {"@types/lodash.merge": "^4.6.7", "@types/webextension-polyfill": "^0.10.5", "tsconfig": "workspace:*"}, "dependencies": {"lodash.merge": "^4.6.2"}, "buildc": {"outDir": "lib"}}