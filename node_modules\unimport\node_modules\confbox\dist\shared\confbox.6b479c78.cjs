"use strict";const _format=require("./confbox.3768c7e9.cjs");function createScanner(n,l=!1){const g=n.length;let e=0,u="",p=0,k=16,A=0,o=0,O=0,r=0,T=0;function L(t,s){let b=0,c=0;for(;b<t||!s;){let i=n.charCodeAt(e);if(i>=48&&i<=57)c=c*16+i-48;else if(i>=65&&i<=70)c=c*16+i-65+10;else if(i>=97&&i<=102)c=c*16+i-97+10;else break;e++,b++}return b<t&&(c=-1),c}function v(t){e=t,u="",p=0,k=16,T=0}function _(){let t=e;if(n.charCodeAt(e)===48)e++;else for(e++;e<n.length&&isDigit(n.charCodeAt(e));)e++;if(e<n.length&&n.charCodeAt(e)===46)if(e++,e<n.length&&isDigit(n.charCodeAt(e)))for(e++;e<n.length&&isDigit(n.charCodeAt(e));)e++;else return T=3,n.substring(t,e);let s=e;if(e<n.length&&(n.charCodeAt(e)===69||n.charCodeAt(e)===101))if(e++,(e<n.length&&n.charCodeAt(e)===43||n.charCodeAt(e)===45)&&e++,e<n.length&&isDigit(n.charCodeAt(e))){for(e++;e<n.length&&isDigit(n.charCodeAt(e));)e++;s=e}else T=3;return n.substring(t,s)}function U(){let t="",s=e;for(;;){if(e>=g){t+=n.substring(s,e),T=2;break}const b=n.charCodeAt(e);if(b===34){t+=n.substring(s,e),e++;break}if(b===92){if(t+=n.substring(s,e),e++,e>=g){T=2;break}switch(n.charCodeAt(e++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+=`
`;break;case 114:t+="\r";break;case 116:t+="	";break;case 117:const i=L(4,!0);i>=0?t+=String.fromCharCode(i):T=4;break;default:T=5}s=e;continue}if(b>=0&&b<=31)if(isLineBreak(b)){t+=n.substring(s,e),T=2;break}else T=6;e++}return t}function w(){if(u="",T=0,p=e,o=A,r=O,e>=g)return p=g,k=17;let t=n.charCodeAt(e);if(isWhiteSpace(t)){do e++,u+=String.fromCharCode(t),t=n.charCodeAt(e);while(isWhiteSpace(t));return k=15}if(isLineBreak(t))return e++,u+=String.fromCharCode(t),t===13&&n.charCodeAt(e)===10&&(e++,u+=`
`),A++,O=e,k=14;switch(t){case 123:return e++,k=1;case 125:return e++,k=2;case 91:return e++,k=3;case 93:return e++,k=4;case 58:return e++,k=6;case 44:return e++,k=5;case 34:return e++,u=U(),k=10;case 47:const s=e-1;if(n.charCodeAt(e+1)===47){for(e+=2;e<g&&!isLineBreak(n.charCodeAt(e));)e++;return u=n.substring(s,e),k=12}if(n.charCodeAt(e+1)===42){e+=2;const b=g-1;let c=!1;for(;e<b;){const i=n.charCodeAt(e);if(i===42&&n.charCodeAt(e+1)===47){e+=2,c=!0;break}e++,isLineBreak(i)&&(i===13&&n.charCodeAt(e)===10&&e++,A++,O=e)}return c||(e++,T=1),u=n.substring(s,e),k=13}return u+=String.fromCharCode(t),e++,k=16;case 45:if(u+=String.fromCharCode(t),e++,e===g||!isDigit(n.charCodeAt(e)))return k=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return u+=_(),k=11;default:for(;e<g&&N(t);)e++,t=n.charCodeAt(e);if(p!==e){switch(u=n.substring(p,e),u){case"true":return k=8;case"false":return k=9;case"null":return k=7}return k=16}return u+=String.fromCharCode(t),e++,k=16}}function N(t){if(isWhiteSpace(t)||isLineBreak(t))return!1;switch(t){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}function I(){let t;do t=w();while(t>=12&&t<=15);return t}return{setPosition:v,getPosition:()=>e,scan:l?I:w,getToken:()=>k,getTokenValue:()=>u,getTokenOffset:()=>p,getTokenLength:()=>e-p,getTokenStartLine:()=>o,getTokenStartCharacter:()=>p-r,getTokenError:()=>T}}function isWhiteSpace(n){return n===32||n===9}function isLineBreak(n){return n===10||n===13}function isDigit(n){return n>=48&&n<=57}var CharacterCodes;(function(n){n[n.lineFeed=10]="lineFeed",n[n.carriageReturn=13]="carriageReturn",n[n.space=32]="space",n[n._0=48]="_0",n[n._1=49]="_1",n[n._2=50]="_2",n[n._3=51]="_3",n[n._4=52]="_4",n[n._5=53]="_5",n[n._6=54]="_6",n[n._7=55]="_7",n[n._8=56]="_8",n[n._9=57]="_9",n[n.a=97]="a",n[n.b=98]="b",n[n.c=99]="c",n[n.d=100]="d",n[n.e=101]="e",n[n.f=102]="f",n[n.g=103]="g",n[n.h=104]="h",n[n.i=105]="i",n[n.j=106]="j",n[n.k=107]="k",n[n.l=108]="l",n[n.m=109]="m",n[n.n=110]="n",n[n.o=111]="o",n[n.p=112]="p",n[n.q=113]="q",n[n.r=114]="r",n[n.s=115]="s",n[n.t=116]="t",n[n.u=117]="u",n[n.v=118]="v",n[n.w=119]="w",n[n.x=120]="x",n[n.y=121]="y",n[n.z=122]="z",n[n.A=65]="A",n[n.B=66]="B",n[n.C=67]="C",n[n.D=68]="D",n[n.E=69]="E",n[n.F=70]="F",n[n.G=71]="G",n[n.H=72]="H",n[n.I=73]="I",n[n.J=74]="J",n[n.K=75]="K",n[n.L=76]="L",n[n.M=77]="M",n[n.N=78]="N",n[n.O=79]="O",n[n.P=80]="P",n[n.Q=81]="Q",n[n.R=82]="R",n[n.S=83]="S",n[n.T=84]="T",n[n.U=85]="U",n[n.V=86]="V",n[n.W=87]="W",n[n.X=88]="X",n[n.Y=89]="Y",n[n.Z=90]="Z",n[n.asterisk=42]="asterisk",n[n.backslash=92]="backslash",n[n.closeBrace=125]="closeBrace",n[n.closeBracket=93]="closeBracket",n[n.colon=58]="colon",n[n.comma=44]="comma",n[n.dot=46]="dot",n[n.doubleQuote=34]="doubleQuote",n[n.minus=45]="minus",n[n.openBrace=123]="openBrace",n[n.openBracket=91]="openBracket",n[n.plus=43]="plus",n[n.slash=47]="slash",n[n.formFeed=12]="formFeed",n[n.tab=9]="tab"})(CharacterCodes||(CharacterCodes={})),new Array(20).fill(0).map((n,l)=>" ".repeat(l));const maxCachedValues=200;new Array(maxCachedValues).fill(0).map((n,l)=>`
`+" ".repeat(l)),new Array(maxCachedValues).fill(0).map((n,l)=>"\r"+" ".repeat(l)),new Array(maxCachedValues).fill(0).map((n,l)=>`\r
`+" ".repeat(l)),new Array(maxCachedValues).fill(0).map((n,l)=>`
`+"	".repeat(l)),new Array(maxCachedValues).fill(0).map((n,l)=>"\r"+"	".repeat(l)),new Array(maxCachedValues).fill(0).map((n,l)=>`\r
`+"	".repeat(l));var ParseOptions;(function(n){n.DEFAULT={allowTrailingComma:!1}})(ParseOptions||(ParseOptions={}));function parse$1(n,l=[],g=ParseOptions.DEFAULT){let e=null,u=[];const p=[];function k(o){Array.isArray(u)?u.push(o):e!==null&&(u[e]=o)}return visit(n,{onObjectBegin:()=>{const o={};k(o),p.push(u),u=o,e=null},onObjectProperty:o=>{e=o},onObjectEnd:()=>{u=p.pop()},onArrayBegin:()=>{const o=[];k(o),p.push(u),u=o,e=null},onArrayEnd:()=>{u=p.pop()},onLiteralValue:k,onError:(o,O,r)=>{l.push({error:o,offset:O,length:r})}},g),u[0]}function visit(n,l,g=ParseOptions.DEFAULT){const e=createScanner(n,!1),u=[];let p=0;function k(f){return f?()=>p===0&&f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function A(f){return f?m=>p===0&&f(m,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter()):()=>!0}function o(f){return f?m=>p===0&&f(m,e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>u.slice()):()=>!0}function O(f){return f?()=>{p>0?p++:f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter(),()=>u.slice())===!1&&(p=1)}:()=>!0}function r(f){return f?()=>{p>0&&p--,p===0&&f(e.getTokenOffset(),e.getTokenLength(),e.getTokenStartLine(),e.getTokenStartCharacter())}:()=>!0}const T=O(l.onObjectBegin),L=o(l.onObjectProperty),v=r(l.onObjectEnd),_=O(l.onArrayBegin),U=r(l.onArrayEnd),w=o(l.onLiteralValue),N=A(l.onSeparator),I=k(l.onComment),t=A(l.onError),s=g&&g.disallowComments,b=g&&g.allowTrailingComma;function c(){for(;;){const f=e.scan();switch(e.getTokenError()){case 4:i(14);break;case 5:i(15);break;case 3:i(13);break;case 1:s||i(11);break;case 2:i(12);break;case 6:i(16);break}switch(f){case 12:case 13:s?i(10):I();break;case 16:i(1);break;case 15:case 14:break;default:return f}}}function i(f,m=[],j=[]){if(t(f),m.length+j.length>0){let B=e.getToken();for(;B!==17;){if(m.indexOf(B)!==-1){c();break}else if(j.indexOf(B)!==-1)break;B=c()}}}function F(f){const m=e.getTokenValue();return f?w(m):(L(m),u.push(m)),c(),!0}function E(){switch(e.getToken()){case 11:const f=e.getTokenValue();let m=Number(f);isNaN(m)&&(i(2),m=0),w(m);break;case 7:w(null);break;case 8:w(!0);break;case 9:w(!1);break;default:return!1}return c(),!0}function J(){return e.getToken()!==10?(i(3,[],[2,5]),!1):(F(!1),e.getToken()===6?(N(":"),c(),V()||i(4,[],[2,5])):i(5,[],[2,5]),u.pop(),!0)}function a(){T(),c();let f=!1;for(;e.getToken()!==2&&e.getToken()!==17;){if(e.getToken()===5){if(f||i(4,[],[]),N(","),c(),e.getToken()===2&&b)break}else f&&i(6,[],[]);J()||i(4,[],[2,5]),f=!0}return v(),e.getToken()!==2?i(7,[2],[]):c(),!0}function y(){_(),c();let f=!0,m=!1;for(;e.getToken()!==4&&e.getToken()!==17;){if(e.getToken()===5){if(m||i(4,[],[]),N(","),c(),e.getToken()===4&&b)break}else m&&i(6,[],[]);f?(u.push(0),f=!1):u[u.length-1]++,V()||i(4,[],[4,5]),m=!0}return U(),f||u.pop(),e.getToken()!==4?i(8,[4],[]):c(),!0}function V(){switch(e.getToken()){case 3:return y();case 1:return a();case 10:return F(!0);default:return E()}}return c(),e.getToken()===17?g.allowEmptyContent?!0:(i(4,[],[]),!1):V()?(e.getToken()!==17&&i(9,[],[]),!0):(i(4,[],[]),!1)}var ScanError;(function(n){n[n.None=0]="None",n[n.UnexpectedEndOfComment=1]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=2]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=3]="UnexpectedEndOfNumber",n[n.InvalidUnicode=4]="InvalidUnicode",n[n.InvalidEscapeCharacter=5]="InvalidEscapeCharacter",n[n.InvalidCharacter=6]="InvalidCharacter"})(ScanError||(ScanError={}));var SyntaxKind;(function(n){n[n.OpenBraceToken=1]="OpenBraceToken",n[n.CloseBraceToken=2]="CloseBraceToken",n[n.OpenBracketToken=3]="OpenBracketToken",n[n.CloseBracketToken=4]="CloseBracketToken",n[n.CommaToken=5]="CommaToken",n[n.ColonToken=6]="ColonToken",n[n.NullKeyword=7]="NullKeyword",n[n.TrueKeyword=8]="TrueKeyword",n[n.FalseKeyword=9]="FalseKeyword",n[n.StringLiteral=10]="StringLiteral",n[n.NumericLiteral=11]="NumericLiteral",n[n.LineCommentTrivia=12]="LineCommentTrivia",n[n.BlockCommentTrivia=13]="BlockCommentTrivia",n[n.LineBreakTrivia=14]="LineBreakTrivia",n[n.Trivia=15]="Trivia",n[n.Unknown=16]="Unknown",n[n.EOF=17]="EOF"})(SyntaxKind||(SyntaxKind={}));const parse=parse$1;var ParseErrorCode;(function(n){n[n.InvalidSymbol=1]="InvalidSymbol",n[n.InvalidNumberFormat=2]="InvalidNumberFormat",n[n.PropertyNameExpected=3]="PropertyNameExpected",n[n.ValueExpected=4]="ValueExpected",n[n.ColonExpected=5]="ColonExpected",n[n.CommaExpected=6]="CommaExpected",n[n.CloseBraceExpected=7]="CloseBraceExpected",n[n.CloseBracketExpected=8]="CloseBracketExpected",n[n.EndOfFileExpected=9]="EndOfFileExpected",n[n.InvalidCommentToken=10]="InvalidCommentToken",n[n.UnexpectedEndOfComment=11]="UnexpectedEndOfComment",n[n.UnexpectedEndOfString=12]="UnexpectedEndOfString",n[n.UnexpectedEndOfNumber=13]="UnexpectedEndOfNumber",n[n.InvalidUnicode=14]="InvalidUnicode",n[n.InvalidEscapeCharacter=15]="InvalidEscapeCharacter",n[n.InvalidCharacter=16]="InvalidCharacter"})(ParseErrorCode||(ParseErrorCode={}));function parseJSON(n,l){const g=JSON.parse(n,l?.reviver);return _format.storeFormat(n,g,l),g}function stringifyJSON(n,l){const g=_format.getFormat(n,l),e=JSON.stringify(n,l?.replacer,g.indent);return g.whitespace.start+e+g.whitespace.end}function parseJSONC(n,l){const g=parse(n,l?.errors,l);return _format.storeFormat(n,g,l),g}function stringifyJSONC(n,l){return stringifyJSON(n,l)}exports.parseJSON=parseJSON,exports.parseJSONC=parseJSONC,exports.stringifyJSON=stringifyJSON,exports.stringifyJSONC=stringifyJSONC;
