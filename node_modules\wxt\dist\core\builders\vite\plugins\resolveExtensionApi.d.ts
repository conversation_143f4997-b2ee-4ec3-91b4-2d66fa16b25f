import { ResolvedConfig } from '../../../../types';
import type * as vite from 'vite';
/**
 * Apply the experimental config for which extension API is used. This only
 * effects the extension API included at RUNTIME - during development, types
 * depend on the import.
 *
 * NOTE: this only works if we import `wxt/browser` instead of using the relative path.
 */
export declare function resolveExtensionApi(config: ResolvedConfig): vite.Plugin;
