/**
 * Removes any code used at runtime related to an entrypoint's main function.
 * 1. Removes or clears out `main` function from returned object
 * 2. Removes any unused functions/variables outside the definition that aren't being called/used
 * 3. Removes unused imports
 * 3. Removes value-less, side-effect only imports (like `import "./styles.css"` or `import "webextension-polyfill"`)
 */
export declare function removeMainFunctionCode(code: string): {
    code: string;
    map?: string;
};
