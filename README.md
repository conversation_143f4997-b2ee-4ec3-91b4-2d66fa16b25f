# YouTube Focus Extension

A Chrome extension built with the WXT framework that provides a focused YouTube experience by filtering out distracting recommendations and providing a clean search interface.

## Features

### Homepage Experience

- **Search Modal**: When visiting <PERSON>'s homepage, a modal dialog appears with a search box
- **Direct Search**: Search for specific videos directly through the modal
- **Clean Interface**: No distracting homepage recommendations

### Video Page Experience

- **Hide Recommendations**: Removes all sidebar recommendations and "up next" suggestions
- **Clean Video Viewing**: Focus only on the current video content
- **Preserve Controls**: Keeps essential video controls (play/pause, volume, etc.) intact
- **Remove Distractions**: Hides end screens, cards, and autoplay suggestions

## Installation

### Development Installation

1. **Clone and Setup**:

   ```bash
   git clone <repository-url>
   cd youtube-chajian
   npm install
   ```

2. **Build the Extension**:

   ```bash
   npm run build
   ```

3. **Load in Chrome**:
   - Open Chrome and go to `chrome://extensions/`
   - Enable "Developer mode" in the top right
   - Click "Load unpacked"
   - Select the `dist/chrome-mv3` folder (clean production build)

### Production Installation

1. **Build for Production**:

   ```bash
   npm run build
   npm run zip
   ```

2. **Install the ZIP**:
   - The extension will be packaged in `dist/chrome-mv3.zip`
   - Upload to Chrome Web Store or install manually

## Usage

1. **Install the extension** following the instructions above
2. **Visit YouTube** (youtube.com)
3. **On the homepage**: A search modal will appear automatically
   - Type your search query
   - Press Enter or click Search
   - You'll be redirected to search results
4. **On video pages**: Recommendations will be automatically hidden
   - Only the video player and essential controls remain visible
   - No sidebar distractions or autoplay suggestions

## Development

### Available Scripts

- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run zip` - Create distributable ZIP file
- `npm run dev:firefox` - Development for Firefox
- `npm run build:firefox` - Build for Firefox

### Project Structure

```
youtube-chajian/
├── entrypoints/
│   ├── popup.html              # Extension popup
│   └── youtube.content.ts      # Main content script
├── assets/
│   └── youtube.css            # Styles for the extension
├── wxt.config.ts              # WXT configuration
├── package.json               # Dependencies and scripts
└── README.md                  # This file
```

### Key Files

- **`entrypoints/youtube.content.ts`**: Main logic for homepage modal and video page filtering
- **`assets/youtube.css`**: Styles for the modal and hiding YouTube elements
- **`wxt.config.ts`**: Extension configuration and permissions

## Technical Details

### Permissions Required

- `activeTab`: Access to the current tab for content script injection
- `storage`: Store user preferences (future feature)
- `host_permissions`: Access to YouTube domains

### Content Script Features

- **URL Detection**: Automatically detects homepage vs video pages
- **SPA Navigation**: Handles YouTube's single-page application navigation
- **DOM Manipulation**: Safely hides recommendation elements
- **Modal Management**: Creates and manages search modal interface

### Browser Compatibility

- **Chrome**: Full support (Manifest V3)
- **Firefox**: Compatible with `npm run build:firefox`
- **Edge**: Should work with Chrome build

## Customization

You can customize the extension by modifying:

1. **Selectors**: Update the CSS selectors in `youtube.content.ts` to hide different elements
2. **Styling**: Modify `assets/youtube.css` to change the modal appearance
3. **Behavior**: Adjust the logic in `youtube.content.ts` for different functionality

## Troubleshooting

### Extension Not Working

- Ensure you've loaded the correct folder (`.output/chrome-mv3`)
- Check that the extension is enabled in Chrome
- Refresh YouTube pages after installation

### Modal Not Appearing

- Check browser console for errors
- Ensure you're on the YouTube homepage (not a video page)
- Try disabling other YouTube extensions temporarily

### Recommendations Still Showing

- The extension targets specific CSS selectors that may change
- Check browser console for errors
- YouTube may have updated their layout

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on YouTube
5. Submit a pull request

## License

This project is open source and available under the MIT License.
