{"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["../../source/utils/merge.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,QAAQ,EAAC,MAAM,SAAS,CAAC;AAEjC,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,GAAG,OAA4C,EAAoB,EAAE;IACrG,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,MAAM,KAAK,SAAS,EAAE,CAAC;YAC1E,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;QACjE,CAAC;IACF,CAAC;IAED,OAAO,SAAS,CAAC,EAAE,EAAE,GAAG,OAAO,CAAC,CAAC;AAClC,CAAC,CAAC;AAEF,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,UAAyB,EAAE,EAAE,UAAyB,EAAE,EAAE,EAAE;IACxF,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,OAAiC,CAAC,CAAC;IACzE,MAAM,iBAAiB,GAAG,OAAO,YAAY,UAAU,CAAC,OAAO,CAAC;IAChE,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,OAAO,CAAC,OAAiC,CAAC,CAAC;IAEzE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;QAC7C,IAAI,CAAC,iBAAiB,IAAI,KAAK,KAAK,WAAW,CAAC,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;YACzE,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACpB,CAAC;aAAM,CAAC;YACP,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QACxB,CAAC;IACF,CAAC;IAED,OAAO,MAAM,CAAC;AACf,CAAC,CAAC;AAEF,SAAS,YAAY,CAAwB,QAAe,EAAE,QAAe,EAAE,QAAW;IACzF,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,SAAS,CAAC;QAC7E,CAAC,CAAC,EAAE;QACJ,CAAC,CAAC,SAAS,CAAqB,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;AACtF,CAAC;AAED,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,WAAkB,EAAE,EAAE,WAAkB,EAAE,EAAmB,EAAE,CAAC,CAC1F;IACC,aAAa,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC;IAChE,WAAW,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;IAC5D,aAAa,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,eAAe,CAAC;IAChE,WAAW,EAAE,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,aAAa,CAAC;CAC5D,CACD,CAAC;AAEF,6CAA6C;AAC7C,MAAM,CAAC,MAAM,SAAS,GAAG,CAAI,GAAG,OAAsC,EAAK,EAAE;IAC5E,IAAI,WAAW,GAAQ,EAAE,CAAC;IAC1B,IAAI,OAAO,GAAG,EAAE,CAAC;IACjB,IAAI,KAAK,GAAG,EAAE,CAAC;IAEf,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;QAC9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBACjC,WAAW,GAAG,EAAE,CAAC;YAClB,CAAC;YAED,WAAW,GAAG,CAAC,GAAG,WAAW,EAAE,GAAG,MAAM,CAAC,CAAC;QAC3C,CAAC;aAAM,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;gBACjD,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,WAAW,EAAE,CAAC;oBAC3C,KAAK,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC5C,CAAC;gBAED,WAAW,GAAG,EAAC,GAAG,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAC,CAAC;YAC9C,CAAC;YAED,IAAI,QAAQ,CAAE,MAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBACrC,KAAK,GAAG,UAAU,CAAC,KAAK,EAAG,MAAc,CAAC,KAAK,CAAC,CAAC;gBACjD,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;YAC3B,CAAC;YAED,IAAI,QAAQ,CAAE,MAAc,CAAC,OAAO,CAAC,EAAE,CAAC;gBACvC,OAAO,GAAG,YAAY,CAAC,OAAO,EAAG,MAAc,CAAC,OAAO,CAAC,CAAC;gBACzD,WAAW,CAAC,OAAO,GAAG,OAAO,CAAC;YAC/B,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,WAAW,CAAC;AACpB,CAAC,CAAC", "sourcesContent": ["import type {KyHeadersInit, Options} from '../types/options.js';\nimport type {Hooks} from '../types/hooks.js';\nimport {isObject} from './is.js';\n\nexport const validateAndMerge = (...sources: Array<Partial<Options> | undefined>): Partial<Options> => {\n\tfor (const source of sources) {\n\t\tif ((!isObject(source) || Array.isArray(source)) && source !== undefined) {\n\t\t\tthrow new TypeError('The `options` argument must be an object');\n\t\t}\n\t}\n\n\treturn deepMerge({}, ...sources);\n};\n\nexport const mergeHeaders = (source1: KyHeadersInit = {}, source2: KyHeadersInit = {}) => {\n\tconst result = new globalThis.Headers(source1 as RequestInit['headers']);\n\tconst isHeadersInstance = source2 instanceof globalThis.Headers;\n\tconst source = new globalThis.Headers(source2 as RequestInit['headers']);\n\n\tfor (const [key, value] of source.entries()) {\n\t\tif ((isHeadersInstance && value === 'undefined') || value === undefined) {\n\t\t\tresult.delete(key);\n\t\t} else {\n\t\t\tresult.set(key, value);\n\t\t}\n\t}\n\n\treturn result;\n};\n\nfunction newHookValue<K extends keyof Hooks>(original: Hooks, incoming: Hooks, property: K): Required<Hooks>[K] {\n\treturn (Object.hasOwn(incoming, property) && incoming[property] === undefined)\n\t\t? []\n\t\t: deepMerge<Required<Hooks>[K]>(original[property] ?? [], incoming[property] ?? []);\n}\n\nexport const mergeHooks = (original: Hooks = {}, incoming: Hooks = {}): Required<Hooks> => (\n\t{\n\t\tbeforeRequest: newHookValue(original, incoming, 'beforeRequest'),\n\t\tbeforeRetry: newHookValue(original, incoming, 'beforeRetry'),\n\t\tafterResponse: newHookValue(original, incoming, 'afterResponse'),\n\t\tbeforeError: newHookValue(original, incoming, 'beforeError'),\n\t}\n);\n\n// TODO: Make this strongly-typed (no `any`).\nexport const deepMerge = <T>(...sources: Array<Partial<T> | undefined>): T => {\n\tlet returnValue: any = {};\n\tlet headers = {};\n\tlet hooks = {};\n\n\tfor (const source of sources) {\n\t\tif (Array.isArray(source)) {\n\t\t\tif (!Array.isArray(returnValue)) {\n\t\t\t\treturnValue = [];\n\t\t\t}\n\n\t\t\treturnValue = [...returnValue, ...source];\n\t\t} else if (isObject(source)) {\n\t\t\tfor (let [key, value] of Object.entries(source)) {\n\t\t\t\tif (isObject(value) && key in returnValue) {\n\t\t\t\t\tvalue = deepMerge(returnValue[key], value);\n\t\t\t\t}\n\n\t\t\t\treturnValue = {...returnValue, [key]: value};\n\t\t\t}\n\n\t\t\tif (isObject((source as any).hooks)) {\n\t\t\t\thooks = mergeHooks(hooks, (source as any).hooks);\n\t\t\t\treturnValue.hooks = hooks;\n\t\t\t}\n\n\t\t\tif (isObject((source as any).headers)) {\n\t\t\t\theaders = mergeHeaders(headers, (source as any).headers);\n\t\t\t\treturnValue.headers = headers;\n\t\t\t}\n\t\t}\n\t}\n\n\treturn returnValue;\n};\n"]}