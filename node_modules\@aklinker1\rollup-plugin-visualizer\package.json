{"name": "@aklinker1/rollup-plugin-visualizer", "version": "5.12.0", "main": "./dist/plugin/index.js", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bin": "./dist/bin/cli.js", "files": ["dist"], "repository": {"type": "git", "url": "**************:btd/rollup-plugin-visualizer.git"}, "homepage": "https://github.com/btd/rollup-plugin-visualizer", "bugs": {"url": "https://github.com/btd/rollup-plugin-visualizer/issues"}, "scripts": {"lint": "eslint 'plugin/**/*.ts' 'src/**/*.{ts,tsx}'", "build": "run-p build:*", "build:plugin": "tsc", "build:frontend": "rollup -c rollup.config.js", "build-dev": "rollup -c rollup.config-dev.js", "clean": "del-cli dist", "prebuild": "npm run clean", "test": "jest"}, "dependencies": {"open": "^8.4.0", "picomatch": "^2.3.1", "source-map": "^0.7.4", "yargs": "^17.5.1"}, "peerDependencies": {"rollup": "2.x || 3.x || 4.x"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "devDependencies": {"@jest/globals": "^29.3.1", "@rollup/plugin-alias": "^5.0.0", "@rollup/plugin-commonjs": "^25.0.1", "@rollup/plugin-node-resolve": "^15.0.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.1", "@types/bytes": "^3.1.1", "@types/d3-array": "^3.0.3", "@types/d3-color": "^3.1.0", "@types/d3-force": "^3.0.3", "@types/d3-hierarchy": "^3.1.0", "@types/d3-scale": "^4.0.2", "@types/d3-shape": "^3.1.0", "@types/node": "^18.8.5", "@types/picomatch": "^2.3.0", "@types/yargs": "^17.0.10", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "bytes": "^3.1.2", "d3-array": "^3.1.6", "d3-color": "^3.1.0", "d3-force": "^3.0.0", "d3-hierarchy": "^3.1.2", "d3-scale": "^4.0.2", "d3-shape": "^3.1.0", "del-cli": "^5.0.0", "eslint": "^8.16.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.3.1", "npm-run-all": "^4.1.5", "picomatch-browser": "^2.2.6", "postcss": "^8.4.14", "postcss-url": "^10.1.3", "preact": "^10.7.2", "prettier": "^3.1.0", "rollup": "^4.5.2", "rollup-plugin-postcss": "^4.0.2", "sass": "^1.52.1", "ts-jest": "^29.0.3", "typescript": "~5.3.2"}, "engines": {"node": ">=14"}, "keywords": ["rollup-plugin", "visualizer", "network", "treemap", "sunburst", "diagram"]}