{"version": 3, "file": "TimeoutError.js", "sourceRoot": "", "sources": ["../../source/errors/TimeoutError.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,YAAa,SAAQ,KAAK;IAC/B,OAAO,CAAY;IAE1B,YAAY,OAAgB;QAC3B,KAAK,CAAC,sBAAsB,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,IAAI,GAAG,cAAc,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;CACD", "sourcesContent": ["import type {KyRequest} from '../types/request.js';\n\nexport class TimeoutError extends Error {\n\tpublic request: KyRequest;\n\n\tconstructor(request: Request) {\n\t\tsuper(`Request timed out: ${request.method} ${request.url}`);\n\t\tthis.name = 'TimeoutError';\n\t\tthis.request = request;\n\t}\n}\n"]}