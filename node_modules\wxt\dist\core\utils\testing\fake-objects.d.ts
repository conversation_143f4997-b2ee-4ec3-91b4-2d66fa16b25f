import type { Manifest } from 'wxt/browser';
import { ResolvedConfig, WxtDevServer, BackgroundEntrypoint, ContentScriptEntrypoint, GenericEntrypoint, OptionsEntrypoint, PopupEntrypoint, OutputChunk, OutputFile, OutputAsset, BuildOutput, BuildStepOutput, UserManifest, Wxt, SidepanelEntrypoint, BaseEntrypoint } from '../../../types';
type DeepPartial<T> = T extends object ? {
    [P in keyof T]?: DeepPartial<T[P]>;
} : T;
export declare function fakeFileName(): string;
export declare function fakeFile(root?: string): string;
export declare function fakeDir(root?: string): string;
export declare const fakeEntrypoint: (options?: DeepPartial<BaseEntrypoint>) => GenericEntrypoint | BackgroundEntrypoint | ContentScriptEntrypoint | PopupEntrypoint | OptionsEntrypoint;
export declare const fakeContentScriptEntrypoint: (overrides?: {
    type?: "content-script" | undefined;
    options?: {
        world?: "ISOLATED" | "MAIN" | undefined;
        matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
        runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
        matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
        excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
        includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
        excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
        allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
        matchOriginAsFallback?: boolean | undefined;
        cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
        registration?: "manifest" | "runtime" | undefined;
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => ContentScriptEntrypoint;
export declare const fakeBackgroundEntrypoint: (overrides?: {
    type?: "background" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
        persistent?: boolean | undefined;
        type?: "module" | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => BackgroundEntrypoint;
export declare const fakeUnlistedScriptEntrypoint: (overrides?: {
    type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => GenericEntrypoint;
export declare const fakeOptionsEntrypoint: (overrides?: {
    type?: "options" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
        browserStyle?: boolean | undefined;
        openInTab?: boolean | undefined;
        chromeStyle?: boolean | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => OptionsEntrypoint;
export declare const fakePopupEntrypoint: (overrides?: {
    type?: "popup" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
        mv2Key?: "browser_action" | "page_action" | undefined;
        defaultTitle?: string | undefined;
        browserStyle?: boolean | undefined;
        defaultIcon?: {
            [x: string]: string | undefined;
        } | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => PopupEntrypoint;
export declare const fakeSidepanelEntrypoint: (overrides?: {
    type?: "sidepanel" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
        defaultTitle?: string | undefined;
        browserStyle?: boolean | undefined;
        openAtInstall?: boolean | undefined;
        defaultIcon?: string | {
            [x: string]: string | undefined;
        } | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => SidepanelEntrypoint;
export declare const fakeGenericEntrypoint: (overrides?: {
    type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
    options?: {
        include?: (string | undefined)[] | undefined;
        exclude?: (string | undefined)[] | undefined;
    } | undefined;
    name?: string | undefined;
    inputPath?: string | undefined;
    outputDir?: string | undefined;
    skipped?: boolean | undefined;
} | undefined) => GenericEntrypoint;
export declare const fakeOutputChunk: (overrides?: {
    type?: "chunk" | undefined;
    fileName?: string | undefined;
    moduleIds?: (string | undefined)[] | undefined;
} | undefined) => OutputChunk;
export declare const fakeOutputAsset: (overrides?: {
    type?: "asset" | undefined;
    fileName?: string | undefined;
} | undefined) => OutputAsset;
export declare function fakeOutputFile(): OutputFile;
export declare const fakeManifest: (overrides?: any) => Manifest.WebExtensionManifest;
export declare const fakeUserManifest: (overrides?: {
    [x: string]: any;
    content_scripts?: ({
        matches?: (string | undefined)[] | undefined;
        exclude_matches?: (string | undefined)[] | undefined;
        css?: (string | undefined)[] | undefined;
        js?: (string | undefined)[] | undefined;
        run_at?: string | undefined;
        all_frames?: boolean | undefined;
        match_about_blank?: boolean | undefined;
        include_globs?: (string | undefined)[] | undefined;
        exclude_globs?: (string | undefined)[] | undefined;
        world?: "ISOLATED" | "MAIN" | undefined;
    } | undefined)[] | undefined;
    content_security_policy?: {
        extension_pages?: string | undefined;
        sandbox?: string | undefined;
    } | undefined;
    host_permissions?: (string | undefined)[] | undefined;
    optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
    optional_host_permissions?: (string | undefined)[] | undefined;
    name?: string | undefined;
    version?: string | undefined;
    default_locale?: string | undefined;
    description?: string | undefined;
    icons?: {
        [x: number]: string | undefined;
    } | undefined;
    author?: {
        email?: string | undefined;
    } | undefined;
    background_page?: string | undefined;
    chrome_settings_overrides?: {
        homepage?: string | undefined;
        search_provider?: {
            name?: string | undefined;
            keyword?: string | undefined;
            favicon_url?: string | undefined;
            search_url?: string | undefined;
            encoding?: string | undefined;
            suggest_url?: string | undefined;
            instant_url?: string | undefined;
            image_url?: string | undefined;
            search_url_post_params?: string | undefined;
            suggest_url_post_params?: string | undefined;
            instant_url_post_params?: string | undefined;
            image_url_post_params?: string | undefined;
            alternate_urls?: (string | undefined)[] | undefined;
            prepopulated_id?: number | undefined;
            is_default?: boolean | undefined;
        } | undefined;
        startup_pages?: (string | undefined)[] | undefined;
    } | undefined;
    chrome_ui_overrides?: {
        bookmarks_ui?: {
            remove_bookmark_shortcut?: boolean | undefined;
            remove_button?: boolean | undefined;
        } | undefined;
    } | undefined;
    commands?: {
        [x: string]: {
            suggested_key?: {
                default?: string | undefined;
                windows?: string | undefined;
                mac?: string | undefined;
                chromeos?: string | undefined;
                linux?: string | undefined;
            } | undefined;
            description?: string | undefined;
            global?: boolean | undefined;
        } | undefined;
    } | undefined;
    content_capabilities?: {
        matches?: (string | undefined)[] | undefined;
        permissions?: (string | undefined)[] | undefined;
    } | undefined;
    converted_from_user_script?: boolean | undefined;
    current_locale?: string | undefined;
    event_rules?: ({
        event?: string | undefined;
        actions?: ({
            type?: string | undefined;
        } | undefined)[] | undefined;
        conditions?: ({
            pageUrl?: {
                hostContains?: string | undefined;
                hostEquals?: string | undefined;
                hostPrefix?: string | undefined;
                hostSuffix?: string | undefined;
                pathContains?: string | undefined;
                pathEquals?: string | undefined;
                pathPrefix?: string | undefined;
                pathSuffix?: string | undefined;
                queryContains?: string | undefined;
                queryEquals?: string | undefined;
                queryPrefix?: string | undefined;
                querySuffix?: string | undefined;
                urlContains?: string | undefined;
                urlEquals?: string | undefined;
                urlMatches?: string | undefined;
                originAndPathMatches?: string | undefined;
                urlPrefix?: string | undefined;
                urlSuffix?: string | undefined;
                schemes?: (string | undefined)[] | undefined;
                ports?: (number | (number | undefined)[] | undefined)[] | undefined;
            } | undefined;
            css?: (string | undefined)[] | undefined;
            isBookmarked?: boolean | undefined;
        } | undefined)[] | undefined;
    } | undefined)[] | undefined;
    externally_connectable?: {
        ids?: (string | undefined)[] | undefined;
        matches?: (string | undefined)[] | undefined;
        accepts_tls_channel_id?: boolean | undefined;
    } | undefined;
    file_browser_handlers?: ({
        id?: string | undefined;
        default_title?: string | undefined;
        file_filters?: (string | undefined)[] | undefined;
    } | undefined)[] | undefined;
    file_system_provider_capabilities?: {
        configurable?: boolean | undefined;
        watchable?: boolean | undefined;
        multiple_mounts?: boolean | undefined;
        source?: string | undefined;
    } | undefined;
    homepage_url?: string | undefined;
    import?: ({
        id?: string | undefined;
        minimum_version?: string | undefined;
    } | undefined)[] | undefined;
    export?: {
        whitelist?: (string | undefined)[] | undefined;
    } | undefined;
    incognito?: string | undefined;
    input_components?: ({
        name?: string | undefined;
        type?: string | undefined;
        id?: string | undefined;
        description?: string | undefined;
        language?: string | (string | undefined)[] | undefined;
        layouts?: (string | undefined)[] | undefined;
        indicator?: string | undefined;
    } | undefined)[] | undefined;
    key?: string | undefined;
    minimum_chrome_version?: string | undefined;
    nacl_modules?: ({
        path?: string | undefined;
        mime_type?: string | undefined;
    } | undefined)[] | undefined;
    oauth2?: {
        client_id?: string | undefined;
        scopes?: (string | undefined)[] | undefined;
    } | undefined;
    offline_enabled?: boolean | undefined;
    omnibox?: {
        keyword?: string | undefined;
    } | undefined;
    platforms?: ({
        nacl_arch?: string | undefined;
        sub_package_path?: string | undefined;
    } | undefined)[] | undefined;
    plugins?: ({
        path?: string | undefined;
    } | undefined)[] | undefined;
    requirements?: {
        "3D"?: {
            features?: (string | undefined)[] | undefined;
        } | undefined;
        plugins?: {
            npapi?: boolean | undefined;
        } | undefined;
    } | undefined;
    short_name?: string | undefined;
    spellcheck?: {
        dictionary_language?: string | undefined;
        dictionary_locale?: string | undefined;
        dictionary_format?: string | undefined;
        dictionary_path?: string | undefined;
    } | undefined;
    storage?: {
        managed_schema?: string | undefined;
    } | undefined;
    tts_engine?: {
        voices?: ({
            voice_name?: string | undefined;
            lang?: string | undefined;
            gender?: string | undefined;
            event_types?: (string | undefined)[] | undefined;
        } | undefined)[] | undefined;
    } | undefined;
    update_url?: string | undefined;
    version_name?: string | undefined;
    action?: {
        default_icon?: {
            [x: number]: string | undefined;
        } | undefined;
        default_title?: string | undefined;
        default_popup?: string | undefined;
        browser_style?: boolean | undefined;
    } | undefined;
    browser_action?: {
        default_icon?: {
            [x: number]: string | undefined;
        } | undefined;
        default_title?: string | undefined;
        default_popup?: string | undefined;
        browser_style?: boolean | undefined;
    } | undefined;
    page_action?: {
        default_icon?: {
            [x: number]: string | undefined;
        } | undefined;
        default_title?: string | undefined;
        default_popup?: string | undefined;
        browser_style?: boolean | undefined;
    } | undefined;
    browser_specific_settings?: {
        gecko?: {
            id?: string | undefined;
            strict_min_version?: string | undefined;
            strict_max_version?: string | undefined;
            update_url?: string | undefined;
        } | undefined;
        gecko_android?: {
            strict_min_version?: string | undefined;
            strict_max_version?: string | undefined;
        } | undefined;
        safari?: {
            strict_min_version?: string | undefined;
            strict_max_version?: string | undefined;
        } | undefined;
    } | undefined;
    permissions?: (chrome.runtime.ManifestPermissions | {
        readonly [x: number]: string | undefined;
        toString?: {} | undefined;
        charAt?: {} | undefined;
        charCodeAt?: {} | undefined;
        concat?: {} | undefined;
        indexOf?: {} | undefined;
        lastIndexOf?: {} | undefined;
        localeCompare?: {} | undefined;
        match?: {} | undefined;
        replace?: {} | undefined;
        search?: {} | undefined;
        slice?: {} | undefined;
        split?: {} | undefined;
        substring?: {} | undefined;
        toLowerCase?: {} | undefined;
        toLocaleLowerCase?: {} | undefined;
        toUpperCase?: {} | undefined;
        toLocaleUpperCase?: {} | undefined;
        trim?: {} | undefined;
        readonly length?: number | undefined;
        substr?: {} | undefined;
        valueOf?: {} | undefined;
        codePointAt?: {} | undefined;
        includes?: {} | undefined;
        endsWith?: {} | undefined;
        normalize?: {} | undefined;
        repeat?: {} | undefined;
        startsWith?: {} | undefined;
        anchor?: {} | undefined;
        big?: {} | undefined;
        blink?: {} | undefined;
        bold?: {} | undefined;
        fixed?: {} | undefined;
        fontcolor?: {} | undefined;
        fontsize?: {} | undefined;
        italics?: {} | undefined;
        link?: {} | undefined;
        small?: {} | undefined;
        strike?: {} | undefined;
        sub?: {} | undefined;
        sup?: {} | undefined;
        padStart?: {} | undefined;
        padEnd?: {} | undefined;
        trimEnd?: {} | undefined;
        trimStart?: {} | undefined;
        trimLeft?: {} | undefined;
        trimRight?: {} | undefined;
        matchAll?: {} | undefined;
        [Symbol.iterator]?: {} | undefined;
        at?: {} | undefined;
    } | undefined)[] | undefined;
    web_accessible_resources?: (string | undefined)[] | ({
        resources?: (string | undefined)[] | undefined;
        matches?: (string | undefined)[] | undefined;
    } | undefined)[] | undefined;
} | undefined) => UserManifest;
export declare function fakeArray<T>(createItem: () => T, count?: number): T[];
export declare const fakeResolvedConfig: (overrides?: {
    vite?: {} | undefined;
    root?: string | undefined;
    srcDir?: string | undefined;
    publicDir?: string | undefined;
    wxtDir?: string | undefined;
    typesDir?: string | undefined;
    entrypointsDir?: string | undefined;
    modulesDir?: string | undefined;
    filterEntrypoints?: {
        add?: {} | undefined;
        clear?: {} | undefined;
        delete?: {} | undefined;
        forEach?: {} | undefined;
        has?: {} | undefined;
        readonly size?: number | undefined;
        entries?: {} | undefined;
        keys?: {} | undefined;
        values?: {} | undefined;
        [Symbol.iterator]?: {} | undefined;
        readonly [Symbol.toStringTag]?: string | undefined;
    } | undefined;
    outBaseDir?: string | undefined;
    outDir?: string | undefined;
    debug?: boolean | undefined;
    wxtModuleDir?: string | undefined;
    mode?: string | undefined;
    command?: import("../../../types").WxtCommand | undefined;
    browser?: string | undefined;
    manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
    env?: {
        mode?: string | undefined;
        command?: import("../../../types").WxtCommand | undefined;
        browser?: string | undefined;
        manifestVersion?: 2 | 3 | undefined;
    } | undefined;
    logger?: {
        debug?: {} | undefined;
        log?: {} | undefined;
        info?: {} | undefined;
        warn?: {} | undefined;
        error?: {} | undefined;
        fatal?: {} | undefined;
        success?: {} | undefined;
        level?: 0 | 1 | 2 | 3 | 4 | 5 | {
            toString?: {} | undefined;
            toFixed?: {} | undefined;
            toExponential?: {} | undefined;
            toPrecision?: {} | undefined;
            valueOf?: {} | undefined;
            toLocaleString?: {} | undefined;
        } | undefined;
    } | undefined;
    imports?: false | {
        imports?: ({
            name?: string | undefined;
            as?: import("unimport").ImportName | undefined;
            with?: {
                [x: string]: string | undefined;
            } | undefined;
            from?: string | undefined;
            priority?: number | undefined;
            disabled?: boolean | undefined;
            dtsDisabled?: boolean | undefined;
            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
            meta?: {
                [x: string]: any;
                description?: string | undefined;
                docsUrl?: string | undefined;
            } | undefined;
            type?: boolean | undefined;
            typeFrom?: import("unimport").ModuleId | undefined;
        } | undefined)[] | undefined;
        presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
            imports?: (string | any | {
                meta?: {
                    [x: string]: any;
                    description?: string | undefined;
                    docsUrl?: string | undefined;
                } | undefined;
                name?: string | undefined;
                type?: boolean | undefined;
                as?: import("unimport").ImportName | undefined;
                with?: {
                    [x: string]: string | undefined;
                } | undefined;
                priority?: number | undefined;
                disabled?: boolean | undefined;
                dtsDisabled?: boolean | undefined;
                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                typeFrom?: import("unimport").ModuleId | undefined;
            } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
            from?: string | undefined;
            priority?: number | undefined;
            disabled?: boolean | undefined;
            dtsDisabled?: boolean | undefined;
            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
            meta?: {
                [x: string]: any;
                description?: string | undefined;
                docsUrl?: string | undefined;
            } | undefined;
            type?: boolean | undefined;
            typeFrom?: import("unimport").ModuleId | undefined;
        } | {
            package?: string | undefined;
            url?: string | undefined;
            ignore?: (string | {
                exec?: {} | undefined;
                test?: {} | undefined;
                readonly source?: string | undefined;
                readonly global?: boolean | undefined;
                readonly ignoreCase?: boolean | undefined;
                readonly multiline?: boolean | undefined;
                lastIndex?: number | undefined;
                compile?: {} | undefined;
                readonly flags?: string | undefined;
                readonly sticky?: boolean | undefined;
                readonly unicode?: boolean | undefined;
                readonly dotAll?: boolean | undefined;
                [Symbol.match]?: {} | undefined;
                [Symbol.replace]?: {} | undefined;
                [Symbol.search]?: {} | undefined;
                [Symbol.split]?: {} | undefined;
                [Symbol.matchAll]?: {} | undefined;
            } | {} | undefined)[] | undefined;
            cache?: boolean | undefined;
        } | undefined)[] | undefined;
        warn?: {} | undefined;
        debugLog?: {} | undefined;
        addons?: {
            addons?: ({
                name?: string | undefined;
                transform?: {} | undefined;
                declaration?: {} | undefined;
                matchImports?: {} | undefined;
                extendImports?: {} | undefined;
                injectImportsResolved?: {} | undefined;
                injectImportsStringified?: {} | undefined;
            } | undefined)[] | undefined;
            vueTemplate?: boolean | undefined;
            vueDirectives?: true | {
                isDirective?: {} | undefined;
            } | undefined;
        } | ({
            name?: string | undefined;
            transform?: {} | undefined;
            declaration?: {} | undefined;
            matchImports?: {} | undefined;
            extendImports?: {} | undefined;
            injectImportsResolved?: {} | undefined;
            injectImportsStringified?: {} | undefined;
        } | undefined)[] | undefined;
        virtualImports?: (string | undefined)[] | undefined;
        dirs?: (string | undefined)[] | undefined;
        dirsScanOptions?: {
            filePatterns?: (string | undefined)[] | undefined;
            fileFilter?: {} | undefined;
            types?: boolean | undefined;
            cwd?: string | undefined;
        } | undefined;
        resolveId?: {} | undefined;
        commentsDisable?: (string | undefined)[] | undefined;
        commentsDebug?: (string | undefined)[] | undefined;
        collectMeta?: boolean | undefined;
        injectAtEnd?: boolean | undefined;
        mergeExisting?: boolean | undefined;
        parser?: ("acorn" | "regex") | undefined;
        eslintrc?: {
            enabled?: false | 9 | 8 | undefined;
            filePath?: string | undefined;
            globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
        } | undefined;
    } | undefined;
    manifest?: {
        [x: string]: any;
        content_scripts?: ({
            matches?: (string | undefined)[] | undefined;
            exclude_matches?: (string | undefined)[] | undefined;
            css?: (string | undefined)[] | undefined;
            js?: (string | undefined)[] | undefined;
            run_at?: string | undefined;
            all_frames?: boolean | undefined;
            match_about_blank?: boolean | undefined;
            include_globs?: (string | undefined)[] | undefined;
            exclude_globs?: (string | undefined)[] | undefined;
            world?: "ISOLATED" | "MAIN" | undefined;
        } | undefined)[] | undefined;
        content_security_policy?: {
            extension_pages?: string | undefined;
            sandbox?: string | undefined;
        } | undefined;
        host_permissions?: (string | undefined)[] | undefined;
        optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
        optional_host_permissions?: (string | undefined)[] | undefined;
        name?: string | undefined;
        version?: string | undefined;
        default_locale?: string | undefined;
        description?: string | undefined;
        icons?: {
            [x: number]: string | undefined;
        } | undefined;
        author?: {
            email?: string | undefined;
        } | undefined;
        background_page?: string | undefined;
        chrome_settings_overrides?: {
            homepage?: string | undefined;
            search_provider?: {
                name?: string | undefined;
                keyword?: string | undefined;
                favicon_url?: string | undefined;
                search_url?: string | undefined;
                encoding?: string | undefined;
                suggest_url?: string | undefined;
                instant_url?: string | undefined;
                image_url?: string | undefined;
                search_url_post_params?: string | undefined;
                suggest_url_post_params?: string | undefined;
                instant_url_post_params?: string | undefined;
                image_url_post_params?: string | undefined;
                alternate_urls?: (string | undefined)[] | undefined;
                prepopulated_id?: number | undefined;
                is_default?: boolean | undefined;
            } | undefined;
            startup_pages?: (string | undefined)[] | undefined;
        } | undefined;
        chrome_ui_overrides?: {
            bookmarks_ui?: {
                remove_bookmark_shortcut?: boolean | undefined;
                remove_button?: boolean | undefined;
            } | undefined;
        } | undefined;
        commands?: {
            [x: string]: {
                suggested_key?: {
                    default?: string | undefined;
                    windows?: string | undefined;
                    mac?: string | undefined;
                    chromeos?: string | undefined;
                    linux?: string | undefined;
                } | undefined;
                description?: string | undefined;
                global?: boolean | undefined;
            } | undefined;
        } | undefined;
        content_capabilities?: {
            matches?: (string | undefined)[] | undefined;
            permissions?: (string | undefined)[] | undefined;
        } | undefined;
        converted_from_user_script?: boolean | undefined;
        current_locale?: string | undefined;
        event_rules?: ({
            event?: string | undefined;
            actions?: ({
                type?: string | undefined;
            } | undefined)[] | undefined;
            conditions?: ({
                pageUrl?: {
                    hostContains?: string | undefined;
                    hostEquals?: string | undefined;
                    hostPrefix?: string | undefined;
                    hostSuffix?: string | undefined;
                    pathContains?: string | undefined;
                    pathEquals?: string | undefined;
                    pathPrefix?: string | undefined;
                    pathSuffix?: string | undefined;
                    queryContains?: string | undefined;
                    queryEquals?: string | undefined;
                    queryPrefix?: string | undefined;
                    querySuffix?: string | undefined;
                    urlContains?: string | undefined;
                    urlEquals?: string | undefined;
                    urlMatches?: string | undefined;
                    originAndPathMatches?: string | undefined;
                    urlPrefix?: string | undefined;
                    urlSuffix?: string | undefined;
                    schemes?: (string | undefined)[] | undefined;
                    ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                } | undefined;
                css?: (string | undefined)[] | undefined;
                isBookmarked?: boolean | undefined;
            } | undefined)[] | undefined;
        } | undefined)[] | undefined;
        externally_connectable?: {
            ids?: (string | undefined)[] | undefined;
            matches?: (string | undefined)[] | undefined;
            accepts_tls_channel_id?: boolean | undefined;
        } | undefined;
        file_browser_handlers?: ({
            id?: string | undefined;
            default_title?: string | undefined;
            file_filters?: (string | undefined)[] | undefined;
        } | undefined)[] | undefined;
        file_system_provider_capabilities?: {
            configurable?: boolean | undefined;
            watchable?: boolean | undefined;
            multiple_mounts?: boolean | undefined;
            source?: string | undefined;
        } | undefined;
        homepage_url?: string | undefined;
        import?: ({
            id?: string | undefined;
            minimum_version?: string | undefined;
        } | undefined)[] | undefined;
        export?: {
            whitelist?: (string | undefined)[] | undefined;
        } | undefined;
        incognito?: string | undefined;
        input_components?: ({
            name?: string | undefined;
            type?: string | undefined;
            id?: string | undefined;
            description?: string | undefined;
            language?: string | (string | undefined)[] | undefined;
            layouts?: (string | undefined)[] | undefined;
            indicator?: string | undefined;
        } | undefined)[] | undefined;
        key?: string | undefined;
        minimum_chrome_version?: string | undefined;
        nacl_modules?: ({
            path?: string | undefined;
            mime_type?: string | undefined;
        } | undefined)[] | undefined;
        oauth2?: {
            client_id?: string | undefined;
            scopes?: (string | undefined)[] | undefined;
        } | undefined;
        offline_enabled?: boolean | undefined;
        omnibox?: {
            keyword?: string | undefined;
        } | undefined;
        platforms?: ({
            nacl_arch?: string | undefined;
            sub_package_path?: string | undefined;
        } | undefined)[] | undefined;
        plugins?: ({
            path?: string | undefined;
        } | undefined)[] | undefined;
        requirements?: {
            "3D"?: {
                features?: (string | undefined)[] | undefined;
            } | undefined;
            plugins?: {
                npapi?: boolean | undefined;
            } | undefined;
        } | undefined;
        short_name?: string | undefined;
        spellcheck?: {
            dictionary_language?: string | undefined;
            dictionary_locale?: string | undefined;
            dictionary_format?: string | undefined;
            dictionary_path?: string | undefined;
        } | undefined;
        storage?: {
            managed_schema?: string | undefined;
        } | undefined;
        tts_engine?: {
            voices?: ({
                voice_name?: string | undefined;
                lang?: string | undefined;
                gender?: string | undefined;
                event_types?: (string | undefined)[] | undefined;
            } | undefined)[] | undefined;
        } | undefined;
        update_url?: string | undefined;
        version_name?: string | undefined;
        action?: {
            default_icon?: {
                [x: number]: string | undefined;
            } | undefined;
            default_title?: string | undefined;
            default_popup?: string | undefined;
            browser_style?: boolean | undefined;
        } | undefined;
        browser_action?: {
            default_icon?: {
                [x: number]: string | undefined;
            } | undefined;
            default_title?: string | undefined;
            default_popup?: string | undefined;
            browser_style?: boolean | undefined;
        } | undefined;
        page_action?: {
            default_icon?: {
                [x: number]: string | undefined;
            } | undefined;
            default_title?: string | undefined;
            default_popup?: string | undefined;
            browser_style?: boolean | undefined;
        } | undefined;
        browser_specific_settings?: {
            gecko?: {
                id?: string | undefined;
                strict_min_version?: string | undefined;
                strict_max_version?: string | undefined;
                update_url?: string | undefined;
            } | undefined;
            gecko_android?: {
                strict_min_version?: string | undefined;
                strict_max_version?: string | undefined;
            } | undefined;
            safari?: {
                strict_min_version?: string | undefined;
                strict_max_version?: string | undefined;
            } | undefined;
        } | undefined;
        permissions?: (chrome.runtime.ManifestPermissions | {
            readonly [x: number]: string | undefined;
            toString?: {} | undefined;
            charAt?: {} | undefined;
            charCodeAt?: {} | undefined;
            concat?: {} | undefined;
            indexOf?: {} | undefined;
            lastIndexOf?: {} | undefined;
            localeCompare?: {} | undefined;
            match?: {} | undefined;
            replace?: {} | undefined;
            search?: {} | undefined;
            slice?: {} | undefined;
            split?: {} | undefined;
            substring?: {} | undefined;
            toLowerCase?: {} | undefined;
            toLocaleLowerCase?: {} | undefined;
            toUpperCase?: {} | undefined;
            toLocaleUpperCase?: {} | undefined;
            trim?: {} | undefined;
            readonly length?: number | undefined;
            substr?: {} | undefined;
            valueOf?: {} | undefined;
            codePointAt?: {} | undefined;
            includes?: {} | undefined;
            endsWith?: {} | undefined;
            normalize?: {} | undefined;
            repeat?: {} | undefined;
            startsWith?: {} | undefined;
            anchor?: {} | undefined;
            big?: {} | undefined;
            blink?: {} | undefined;
            bold?: {} | undefined;
            fixed?: {} | undefined;
            fontcolor?: {} | undefined;
            fontsize?: {} | undefined;
            italics?: {} | undefined;
            link?: {} | undefined;
            small?: {} | undefined;
            strike?: {} | undefined;
            sub?: {} | undefined;
            sup?: {} | undefined;
            padStart?: {} | undefined;
            padEnd?: {} | undefined;
            trimEnd?: {} | undefined;
            trimStart?: {} | undefined;
            trimLeft?: {} | undefined;
            trimRight?: {} | undefined;
            matchAll?: {} | undefined;
            [Symbol.iterator]?: {} | undefined;
            at?: {} | undefined;
        } | undefined)[] | undefined;
        web_accessible_resources?: (string | undefined)[] | ({
            resources?: (string | undefined)[] | undefined;
            matches?: (string | undefined)[] | undefined;
        } | undefined)[] | undefined;
    } | undefined;
    fsCache?: {
        set?: {} | undefined;
        get?: {} | undefined;
    } | undefined;
    runnerConfig?: {
        config?: {
            disabled?: boolean | undefined;
            openConsole?: boolean | undefined;
            openDevtools?: boolean | undefined;
            binaries?: {
                [x: string]: string | undefined;
            } | undefined;
            firefoxProfile?: string | undefined;
            chromiumProfile?: string | undefined;
            chromiumPref?: {
                [x: string]: any;
            } | undefined;
            chromiumPort?: number | undefined;
            firefoxPrefs?: {
                [x: string]: string | undefined;
            } | undefined;
            firefoxArgs?: (string | undefined)[] | undefined;
            chromiumArgs?: (string | undefined)[] | undefined;
            startUrls?: (string | undefined)[] | undefined;
            keepProfileChanges?: boolean | undefined;
        } | undefined;
        layers?: ({
            config?: {
                disabled?: boolean | undefined;
                openConsole?: boolean | undefined;
                openDevtools?: boolean | undefined;
                binaries?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxProfile?: string | undefined;
                chromiumProfile?: string | undefined;
                chromiumPref?: {
                    [x: string]: any;
                } | undefined;
                chromiumPort?: number | undefined;
                firefoxPrefs?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxArgs?: (string | undefined)[] | undefined;
                chromiumArgs?: (string | undefined)[] | undefined;
                startUrls?: (string | undefined)[] | undefined;
                keepProfileChanges?: boolean | undefined;
            } | null | undefined;
            source?: string | undefined;
            sourceOptions?: {
                [x: string]: any;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                overrides?: {
                    disabled?: boolean | undefined;
                    openConsole?: boolean | undefined;
                    openDevtools?: boolean | undefined;
                    binaries?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxProfile?: string | undefined;
                    chromiumProfile?: string | undefined;
                    chromiumPref?: {
                        [x: string]: any;
                    } | undefined;
                    chromiumPort?: number | undefined;
                    firefoxPrefs?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxArgs?: (string | undefined)[] | undefined;
                    chromiumArgs?: (string | undefined)[] | undefined;
                    startUrls?: (string | undefined)[] | undefined;
                    keepProfileChanges?: boolean | undefined;
                } | undefined;
                giget?: {
                    provider?: string | undefined;
                    force?: boolean | undefined;
                    forceClean?: boolean | undefined;
                    offline?: boolean | undefined;
                    preferOffline?: boolean | undefined;
                    providers?: {
                        [x: string]: {} | undefined;
                    } | undefined;
                    dir?: string | undefined;
                    registry?: (false | string) | undefined;
                    cwd?: string | undefined;
                    auth?: string | undefined;
                    install?: boolean | undefined;
                    silent?: boolean | undefined;
                } | undefined;
                install?: boolean | undefined;
                auth?: string | undefined;
            } | undefined;
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            cwd?: string | undefined;
            configFile?: string | undefined;
        } | undefined)[] | undefined;
        cwd?: string | undefined;
        source?: string | undefined;
        sourceOptions?: {
            [x: string]: any;
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            overrides?: {
                disabled?: boolean | undefined;
                openConsole?: boolean | undefined;
                openDevtools?: boolean | undefined;
                binaries?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxProfile?: string | undefined;
                chromiumProfile?: string | undefined;
                chromiumPref?: {
                    [x: string]: any;
                } | undefined;
                chromiumPort?: number | undefined;
                firefoxPrefs?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxArgs?: (string | undefined)[] | undefined;
                chromiumArgs?: (string | undefined)[] | undefined;
                startUrls?: (string | undefined)[] | undefined;
                keepProfileChanges?: boolean | undefined;
            } | undefined;
            giget?: {
                provider?: string | undefined;
                force?: boolean | undefined;
                forceClean?: boolean | undefined;
                offline?: boolean | undefined;
                preferOffline?: boolean | undefined;
                providers?: {
                    [x: string]: {} | undefined;
                } | undefined;
                dir?: string | undefined;
                registry?: (false | string) | undefined;
                cwd?: string | undefined;
                auth?: string | undefined;
                install?: boolean | undefined;
                silent?: boolean | undefined;
            } | undefined;
            install?: boolean | undefined;
            auth?: string | undefined;
        } | undefined;
        meta?: {
            [x: string]: any;
            name?: string | undefined;
        } | undefined;
        configFile?: string | undefined;
    } | undefined;
    zip?: {
        name?: string | undefined;
        artifactTemplate?: string | undefined;
        sourcesTemplate?: string | undefined;
        includeSources?: (string | undefined)[] | undefined;
        excludeSources?: (string | undefined)[] | undefined;
        sourcesRoot?: string | undefined;
        downloadedPackagesDir?: string | undefined;
        downloadPackages?: (string | undefined)[] | undefined;
        compressionLevel?: 0 | 1 | 9 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | undefined;
        exclude?: (string | undefined)[] | undefined;
        zipSources?: boolean | undefined;
    } | undefined;
    transformManifest?: {} | undefined;
    analysis?: {
        enabled?: boolean | undefined;
        open?: boolean | undefined;
        template?: NonNullable<import("@aklinker1/rollup-plugin-visualizer/dist/plugin/template-types").TemplateType | undefined> | undefined;
        outputFile?: string | undefined;
        outputDir?: string | undefined;
        outputName?: string | undefined;
        keepArtifacts?: boolean | undefined;
    } | undefined;
    userConfigMetadata?: {
        meta?: {
            [x: string]: any;
            name?: string | undefined;
        } | undefined;
        source?: string | undefined;
        configFile?: string | undefined;
        layers?: ({
            config?: {
                manifest?: {
                    [x: string]: any;
                    content_scripts?: ({
                        matches?: (string | undefined)[] | undefined;
                        exclude_matches?: (string | undefined)[] | undefined;
                        css?: (string | undefined)[] | undefined;
                        js?: (string | undefined)[] | undefined;
                        run_at?: string | undefined;
                        all_frames?: boolean | undefined;
                        match_about_blank?: boolean | undefined;
                        include_globs?: (string | undefined)[] | undefined;
                        exclude_globs?: (string | undefined)[] | undefined;
                        world?: "ISOLATED" | "MAIN" | undefined;
                    } | undefined)[] | undefined;
                    content_security_policy?: {
                        extension_pages?: string | undefined;
                        sandbox?: string | undefined;
                    } | undefined;
                    host_permissions?: (string | undefined)[] | undefined;
                    optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                    optional_host_permissions?: (string | undefined)[] | undefined;
                    name?: string | undefined;
                    version?: string | undefined;
                    default_locale?: string | undefined;
                    description?: string | undefined;
                    icons?: {
                        [x: number]: string | undefined;
                    } | undefined;
                    author?: {
                        email?: string | undefined;
                    } | undefined;
                    background_page?: string | undefined;
                    chrome_settings_overrides?: {
                        homepage?: string | undefined;
                        search_provider?: {
                            name?: string | undefined;
                            keyword?: string | undefined;
                            favicon_url?: string | undefined;
                            search_url?: string | undefined;
                            encoding?: string | undefined;
                            suggest_url?: string | undefined;
                            instant_url?: string | undefined;
                            image_url?: string | undefined;
                            search_url_post_params?: string | undefined;
                            suggest_url_post_params?: string | undefined;
                            instant_url_post_params?: string | undefined;
                            image_url_post_params?: string | undefined;
                            alternate_urls?: (string | undefined)[] | undefined;
                            prepopulated_id?: number | undefined;
                            is_default?: boolean | undefined;
                        } | undefined;
                        startup_pages?: (string | undefined)[] | undefined;
                    } | undefined;
                    chrome_ui_overrides?: {
                        bookmarks_ui?: {
                            remove_bookmark_shortcut?: boolean | undefined;
                            remove_button?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    commands?: {
                        [x: string]: {
                            suggested_key?: {
                                default?: string | undefined;
                                windows?: string | undefined;
                                mac?: string | undefined;
                                chromeos?: string | undefined;
                                linux?: string | undefined;
                            } | undefined;
                            description?: string | undefined;
                            global?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    content_capabilities?: {
                        matches?: (string | undefined)[] | undefined;
                        permissions?: (string | undefined)[] | undefined;
                    } | undefined;
                    converted_from_user_script?: boolean | undefined;
                    current_locale?: string | undefined;
                    event_rules?: ({
                        event?: string | undefined;
                        actions?: ({
                            type?: string | undefined;
                        } | undefined)[] | undefined;
                        conditions?: ({
                            pageUrl?: {
                                hostContains?: string | undefined;
                                hostEquals?: string | undefined;
                                hostPrefix?: string | undefined;
                                hostSuffix?: string | undefined;
                                pathContains?: string | undefined;
                                pathEquals?: string | undefined;
                                pathPrefix?: string | undefined;
                                pathSuffix?: string | undefined;
                                queryContains?: string | undefined;
                                queryEquals?: string | undefined;
                                queryPrefix?: string | undefined;
                                querySuffix?: string | undefined;
                                urlContains?: string | undefined;
                                urlEquals?: string | undefined;
                                urlMatches?: string | undefined;
                                originAndPathMatches?: string | undefined;
                                urlPrefix?: string | undefined;
                                urlSuffix?: string | undefined;
                                schemes?: (string | undefined)[] | undefined;
                                ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                            } | undefined;
                            css?: (string | undefined)[] | undefined;
                            isBookmarked?: boolean | undefined;
                        } | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                    externally_connectable?: {
                        ids?: (string | undefined)[] | undefined;
                        matches?: (string | undefined)[] | undefined;
                        accepts_tls_channel_id?: boolean | undefined;
                    } | undefined;
                    file_browser_handlers?: ({
                        id?: string | undefined;
                        default_title?: string | undefined;
                        file_filters?: (string | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                    file_system_provider_capabilities?: {
                        configurable?: boolean | undefined;
                        watchable?: boolean | undefined;
                        multiple_mounts?: boolean | undefined;
                        source?: string | undefined;
                    } | undefined;
                    homepage_url?: string | undefined;
                    import?: ({
                        id?: string | undefined;
                        minimum_version?: string | undefined;
                    } | undefined)[] | undefined;
                    export?: {
                        whitelist?: (string | undefined)[] | undefined;
                    } | undefined;
                    incognito?: string | undefined;
                    input_components?: ({
                        name?: string | undefined;
                        type?: string | undefined;
                        id?: string | undefined;
                        description?: string | undefined;
                        language?: string | (string | undefined)[] | undefined;
                        layouts?: (string | undefined)[] | undefined;
                        indicator?: string | undefined;
                    } | undefined)[] | undefined;
                    key?: string | undefined;
                    minimum_chrome_version?: string | undefined;
                    nacl_modules?: ({
                        path?: string | undefined;
                        mime_type?: string | undefined;
                    } | undefined)[] | undefined;
                    oauth2?: {
                        client_id?: string | undefined;
                        scopes?: (string | undefined)[] | undefined;
                    } | undefined;
                    offline_enabled?: boolean | undefined;
                    omnibox?: {
                        keyword?: string | undefined;
                    } | undefined;
                    platforms?: ({
                        nacl_arch?: string | undefined;
                        sub_package_path?: string | undefined;
                    } | undefined)[] | undefined;
                    plugins?: ({
                        path?: string | undefined;
                    } | undefined)[] | undefined;
                    requirements?: {
                        "3D"?: {
                            features?: (string | undefined)[] | undefined;
                        } | undefined;
                        plugins?: {
                            npapi?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    short_name?: string | undefined;
                    spellcheck?: {
                        dictionary_language?: string | undefined;
                        dictionary_locale?: string | undefined;
                        dictionary_format?: string | undefined;
                        dictionary_path?: string | undefined;
                    } | undefined;
                    storage?: {
                        managed_schema?: string | undefined;
                    } | undefined;
                    tts_engine?: {
                        voices?: ({
                            voice_name?: string | undefined;
                            lang?: string | undefined;
                            gender?: string | undefined;
                            event_types?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                    } | undefined;
                    update_url?: string | undefined;
                    version_name?: string | undefined;
                    action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    browser_action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    page_action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    browser_specific_settings?: {
                        gecko?: {
                            id?: string | undefined;
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                            update_url?: string | undefined;
                        } | undefined;
                        gecko_android?: {
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                        } | undefined;
                        safari?: {
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                        } | undefined;
                    } | undefined;
                    permissions?: (chrome.runtime.ManifestPermissions | {
                        readonly [x: number]: string | undefined;
                        toString?: {} | undefined;
                        charAt?: {} | undefined;
                        charCodeAt?: {} | undefined;
                        concat?: {} | undefined;
                        indexOf?: {} | undefined;
                        lastIndexOf?: {} | undefined;
                        localeCompare?: {} | undefined;
                        match?: {} | undefined;
                        replace?: {} | undefined;
                        search?: {} | undefined;
                        slice?: {} | undefined;
                        split?: {} | undefined;
                        substring?: {} | undefined;
                        toLowerCase?: {} | undefined;
                        toLocaleLowerCase?: {} | undefined;
                        toUpperCase?: {} | undefined;
                        toLocaleUpperCase?: {} | undefined;
                        trim?: {} | undefined;
                        readonly length?: number | undefined;
                        substr?: {} | undefined;
                        valueOf?: {} | undefined;
                        codePointAt?: {} | undefined;
                        includes?: {} | undefined;
                        endsWith?: {} | undefined;
                        normalize?: {} | undefined;
                        repeat?: {} | undefined;
                        startsWith?: {} | undefined;
                        anchor?: {} | undefined;
                        big?: {} | undefined;
                        blink?: {} | undefined;
                        bold?: {} | undefined;
                        fixed?: {} | undefined;
                        fontcolor?: {} | undefined;
                        fontsize?: {} | undefined;
                        italics?: {} | undefined;
                        link?: {} | undefined;
                        small?: {} | undefined;
                        strike?: {} | undefined;
                        sub?: {} | undefined;
                        sup?: {} | undefined;
                        padStart?: {} | undefined;
                        padEnd?: {} | undefined;
                        trimEnd?: {} | undefined;
                        trimStart?: {} | undefined;
                        trimLeft?: {} | undefined;
                        trimRight?: {} | undefined;
                        matchAll?: {} | undefined;
                        [Symbol.iterator]?: {} | undefined;
                        at?: {} | undefined;
                    } | undefined)[] | undefined;
                    web_accessible_resources?: (string | undefined)[] | ({
                        resources?: (string | undefined)[] | undefined;
                        matches?: (string | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                } | {
                    then?: {} | undefined;
                    catch?: {} | undefined;
                    finally?: {} | undefined;
                    readonly [Symbol.toStringTag]?: string | undefined;
                } | {} | undefined;
                vite?: {} | undefined;
                zip?: {
                    artifactTemplate?: string | undefined;
                    zipSources?: boolean | undefined;
                    sourcesTemplate?: string | undefined;
                    name?: string | undefined;
                    sourcesRoot?: string | undefined;
                    includeSources?: (string | undefined)[] | undefined;
                    excludeSources?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    downloadPackages?: (string | undefined)[] | undefined;
                    compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                } | undefined;
                root?: string | undefined;
                mode?: string | undefined;
                dev?: {
                    server?: {
                        port?: number | undefined;
                        hostname?: string | undefined;
                    } | undefined;
                    reloadCommand?: (string | false) | undefined;
                } | undefined;
                publicDir?: string | undefined;
                experimental?: {} | undefined;
                srcDir?: string | undefined;
                entrypointsDir?: string | undefined;
                modulesDir?: string | undefined;
                filterEntrypoints?: (string | undefined)[] | undefined;
                outDir?: string | undefined;
                outDirTemplate?: string | undefined;
                debug?: boolean | undefined;
                imports?: false | {
                    imports?: ({
                        name?: string | undefined;
                        as?: import("unimport").ImportName | undefined;
                        with?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        from?: string | undefined;
                        priority?: number | undefined;
                        disabled?: boolean | undefined;
                        dtsDisabled?: boolean | undefined;
                        declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                        meta?: {
                            [x: string]: any;
                            description?: string | undefined;
                            docsUrl?: string | undefined;
                        } | undefined;
                        type?: boolean | undefined;
                        typeFrom?: import("unimport").ModuleId | undefined;
                    } | undefined)[] | undefined;
                    presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                        imports?: (string | any | {
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            type?: boolean | undefined;
                            as?: import("unimport").ImportName | undefined;
                            with?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                        from?: string | undefined;
                        priority?: number | undefined;
                        disabled?: boolean | undefined;
                        dtsDisabled?: boolean | undefined;
                        declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                        meta?: {
                            [x: string]: any;
                            description?: string | undefined;
                            docsUrl?: string | undefined;
                        } | undefined;
                        type?: boolean | undefined;
                        typeFrom?: import("unimport").ModuleId | undefined;
                    } | {
                        package?: string | undefined;
                        url?: string | undefined;
                        ignore?: (string | {
                            exec?: {} | undefined;
                            test?: {} | undefined;
                            readonly source?: string | undefined;
                            readonly global?: boolean | undefined;
                            readonly ignoreCase?: boolean | undefined;
                            readonly multiline?: boolean | undefined;
                            lastIndex?: number | undefined;
                            compile?: {} | undefined;
                            readonly flags?: string | undefined;
                            readonly sticky?: boolean | undefined;
                            readonly unicode?: boolean | undefined;
                            readonly dotAll?: boolean | undefined;
                            [Symbol.match]?: {} | undefined;
                            [Symbol.replace]?: {} | undefined;
                            [Symbol.search]?: {} | undefined;
                            [Symbol.split]?: {} | undefined;
                            [Symbol.matchAll]?: {} | undefined;
                        } | {} | undefined)[] | undefined;
                        cache?: boolean | undefined;
                    } | undefined)[] | undefined;
                    warn?: {} | undefined;
                    debugLog?: {} | undefined;
                    addons?: {
                        addons?: ({
                            name?: string | undefined;
                            transform?: {} | undefined;
                            declaration?: {} | undefined;
                            matchImports?: {} | undefined;
                            extendImports?: {} | undefined;
                            injectImportsResolved?: {} | undefined;
                            injectImportsStringified?: {} | undefined;
                        } | undefined)[] | undefined;
                        vueTemplate?: boolean | undefined;
                        vueDirectives?: true | {
                            isDirective?: {} | undefined;
                        } | undefined;
                    } | ({
                        name?: string | undefined;
                        transform?: {} | undefined;
                        declaration?: {} | undefined;
                        matchImports?: {} | undefined;
                        extendImports?: {} | undefined;
                        injectImportsResolved?: {} | undefined;
                        injectImportsStringified?: {} | undefined;
                    } | undefined)[] | undefined;
                    virtualImports?: (string | undefined)[] | undefined;
                    dirs?: (string | undefined)[] | undefined;
                    dirsScanOptions?: {
                        filePatterns?: (string | undefined)[] | undefined;
                        fileFilter?: {} | undefined;
                        types?: boolean | undefined;
                        cwd?: string | undefined;
                    } | undefined;
                    resolveId?: {} | undefined;
                    commentsDisable?: (string | undefined)[] | undefined;
                    commentsDebug?: (string | undefined)[] | undefined;
                    collectMeta?: boolean | undefined;
                    injectAtEnd?: boolean | undefined;
                    mergeExisting?: boolean | undefined;
                    parser?: ("acorn" | "regex") | undefined;
                    eslintrc?: {
                        enabled?: (false | true | "auto" | 8 | 9) | undefined;
                        filePath?: string | undefined;
                        globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                    } | undefined;
                } | undefined;
                browser?: import("../../../types").TargetBrowser | undefined;
                manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                logger?: {
                    debug?: {} | undefined;
                    log?: {} | undefined;
                    info?: {} | undefined;
                    warn?: {} | undefined;
                    error?: {} | undefined;
                    fatal?: {} | undefined;
                    success?: {} | undefined;
                    level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                        toString?: {} | undefined;
                        toFixed?: {} | undefined;
                        toExponential?: {} | undefined;
                        toPrecision?: {} | undefined;
                        valueOf?: {} | undefined;
                        toLocaleString?: {} | undefined;
                    } | undefined;
                } | undefined;
                runner?: {
                    disabled?: boolean | undefined;
                    openConsole?: boolean | undefined;
                    openDevtools?: boolean | undefined;
                    binaries?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxProfile?: string | undefined;
                    chromiumProfile?: string | undefined;
                    chromiumPref?: {
                        [x: string]: any;
                    } | undefined;
                    chromiumPort?: number | undefined;
                    firefoxPrefs?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxArgs?: (string | undefined)[] | undefined;
                    chromiumArgs?: (string | undefined)[] | undefined;
                    startUrls?: (string | undefined)[] | undefined;
                    keepProfileChanges?: boolean | undefined;
                } | undefined;
                transformManifest?: {} | undefined;
                analysis?: {
                    enabled?: boolean | undefined;
                    open?: boolean | undefined;
                    template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                    outputFile?: string | undefined;
                    keepArtifacts?: boolean | undefined;
                } | undefined;
                alias?: {
                    [x: string]: string | undefined;
                } | undefined;
                extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                entrypointLoader?: ("vite-node" | "jiti") | undefined;
                hooks?: {
                    'vite:build:extendConfig'?: {} | undefined;
                    'vite:devServer:extendConfig'?: {} | undefined;
                    ready?: {} | undefined;
                    'config:resolved'?: {} | undefined;
                    'prepare:types'?: {} | undefined;
                    'prepare:publicPaths'?: {} | undefined;
                    'build:before'?: {} | undefined;
                    'build:done'?: {} | undefined;
                    'build:manifestGenerated'?: {} | undefined;
                    'entrypoints:found'?: {} | undefined;
                    'entrypoints:resolved'?: {} | undefined;
                    'entrypoints:grouped'?: {} | undefined;
                    'build:publicAssets'?: {} | undefined;
                    'zip:start'?: {} | undefined;
                    'zip:extension:start'?: {} | undefined;
                    'zip:extension:done'?: {} | undefined;
                    'zip:sources:start'?: {} | undefined;
                    'zip:sources:done'?: {} | undefined;
                    'zip:done'?: {} | undefined;
                    'server:created'?: {} | undefined;
                    'server:started'?: {} | undefined;
                    'server:closed'?: {} | undefined;
                    vite?: {
                        "build:extendConfig"?: {} | undefined;
                        "devServer:extendConfig"?: {} | undefined;
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | {
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    config?: {
                        resolved?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                    } | undefined;
                    prepare?: {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | undefined;
                    build?: {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | undefined;
                    entrypoints?: {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | undefined;
                    zip?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                        "extension:start"?: {} | undefined;
                        "extension:done"?: {} | undefined;
                        "sources:start"?: {} | undefined;
                        "sources:done"?: {} | undefined;
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                    } | {
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    server?: {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | undefined;
                } | {
                    vite?: {
                        "build:extendConfig"?: {} | undefined;
                        "devServer:extendConfig"?: {} | undefined;
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | {
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    config?: {
                        resolved?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                    } | undefined;
                    prepare?: {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | undefined;
                    build?: {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | undefined;
                    entrypoints?: {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | undefined;
                    zip?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                        "extension:start"?: {} | undefined;
                        "extension:done"?: {} | undefined;
                        "sources:start"?: {} | undefined;
                        "sources:done"?: {} | undefined;
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                    } | {
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    server?: {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | undefined;
                    ready?: {} | undefined;
                } | undefined;
                modules?: (string | undefined)[] | undefined;
            } | null | undefined;
            source?: string | undefined;
            sourceOptions?: {
                [x: string]: any;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                overrides?: {
                    manifest?: {
                        [x: string]: any;
                        content_scripts?: ({
                            matches?: (string | undefined)[] | undefined;
                            exclude_matches?: (string | undefined)[] | undefined;
                            css?: (string | undefined)[] | undefined;
                            js?: (string | undefined)[] | undefined;
                            run_at?: string | undefined;
                            all_frames?: boolean | undefined;
                            match_about_blank?: boolean | undefined;
                            include_globs?: (string | undefined)[] | undefined;
                            exclude_globs?: (string | undefined)[] | undefined;
                            world?: "ISOLATED" | "MAIN" | undefined;
                        } | undefined)[] | undefined;
                        content_security_policy?: {
                            extension_pages?: string | undefined;
                            sandbox?: string | undefined;
                        } | undefined;
                        host_permissions?: (string | undefined)[] | undefined;
                        optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                        optional_host_permissions?: (string | undefined)[] | undefined;
                        name?: string | undefined;
                        version?: string | undefined;
                        default_locale?: string | undefined;
                        description?: string | undefined;
                        icons?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        author?: {
                            email?: string | undefined;
                        } | undefined;
                        background_page?: string | undefined;
                        chrome_settings_overrides?: {
                            homepage?: string | undefined;
                            search_provider?: {
                                name?: string | undefined;
                                keyword?: string | undefined;
                                favicon_url?: string | undefined;
                                search_url?: string | undefined;
                                encoding?: string | undefined;
                                suggest_url?: string | undefined;
                                instant_url?: string | undefined;
                                image_url?: string | undefined;
                                search_url_post_params?: string | undefined;
                                suggest_url_post_params?: string | undefined;
                                instant_url_post_params?: string | undefined;
                                image_url_post_params?: string | undefined;
                                alternate_urls?: (string | undefined)[] | undefined;
                                prepopulated_id?: number | undefined;
                                is_default?: boolean | undefined;
                            } | undefined;
                            startup_pages?: (string | undefined)[] | undefined;
                        } | undefined;
                        chrome_ui_overrides?: {
                            bookmarks_ui?: {
                                remove_bookmark_shortcut?: boolean | undefined;
                                remove_button?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        commands?: {
                            [x: string]: {
                                suggested_key?: {
                                    default?: string | undefined;
                                    windows?: string | undefined;
                                    mac?: string | undefined;
                                    chromeos?: string | undefined;
                                    linux?: string | undefined;
                                } | undefined;
                                description?: string | undefined;
                                global?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        content_capabilities?: {
                            matches?: (string | undefined)[] | undefined;
                            permissions?: (string | undefined)[] | undefined;
                        } | undefined;
                        converted_from_user_script?: boolean | undefined;
                        current_locale?: string | undefined;
                        event_rules?: ({
                            event?: string | undefined;
                            actions?: ({
                                type?: string | undefined;
                            } | undefined)[] | undefined;
                            conditions?: ({
                                pageUrl?: {
                                    hostContains?: string | undefined;
                                    hostEquals?: string | undefined;
                                    hostPrefix?: string | undefined;
                                    hostSuffix?: string | undefined;
                                    pathContains?: string | undefined;
                                    pathEquals?: string | undefined;
                                    pathPrefix?: string | undefined;
                                    pathSuffix?: string | undefined;
                                    queryContains?: string | undefined;
                                    queryEquals?: string | undefined;
                                    queryPrefix?: string | undefined;
                                    querySuffix?: string | undefined;
                                    urlContains?: string | undefined;
                                    urlEquals?: string | undefined;
                                    urlMatches?: string | undefined;
                                    originAndPathMatches?: string | undefined;
                                    urlPrefix?: string | undefined;
                                    urlSuffix?: string | undefined;
                                    schemes?: (string | undefined)[] | undefined;
                                    ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                                } | undefined;
                                css?: (string | undefined)[] | undefined;
                                isBookmarked?: boolean | undefined;
                            } | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        externally_connectable?: {
                            ids?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                            accepts_tls_channel_id?: boolean | undefined;
                        } | undefined;
                        file_browser_handlers?: ({
                            id?: string | undefined;
                            default_title?: string | undefined;
                            file_filters?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        file_system_provider_capabilities?: {
                            configurable?: boolean | undefined;
                            watchable?: boolean | undefined;
                            multiple_mounts?: boolean | undefined;
                            source?: string | undefined;
                        } | undefined;
                        homepage_url?: string | undefined;
                        import?: ({
                            id?: string | undefined;
                            minimum_version?: string | undefined;
                        } | undefined)[] | undefined;
                        export?: {
                            whitelist?: (string | undefined)[] | undefined;
                        } | undefined;
                        incognito?: string | undefined;
                        input_components?: ({
                            name?: string | undefined;
                            type?: string | undefined;
                            id?: string | undefined;
                            description?: string | undefined;
                            language?: string | (string | undefined)[] | undefined;
                            layouts?: (string | undefined)[] | undefined;
                            indicator?: string | undefined;
                        } | undefined)[] | undefined;
                        key?: string | undefined;
                        minimum_chrome_version?: string | undefined;
                        nacl_modules?: ({
                            path?: string | undefined;
                            mime_type?: string | undefined;
                        } | undefined)[] | undefined;
                        oauth2?: {
                            client_id?: string | undefined;
                            scopes?: (string | undefined)[] | undefined;
                        } | undefined;
                        offline_enabled?: boolean | undefined;
                        omnibox?: {
                            keyword?: string | undefined;
                        } | undefined;
                        platforms?: ({
                            nacl_arch?: string | undefined;
                            sub_package_path?: string | undefined;
                        } | undefined)[] | undefined;
                        plugins?: ({
                            path?: string | undefined;
                        } | undefined)[] | undefined;
                        requirements?: {
                            "3D"?: {
                                features?: (string | undefined)[] | undefined;
                            } | undefined;
                            plugins?: {
                                npapi?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        short_name?: string | undefined;
                        spellcheck?: {
                            dictionary_language?: string | undefined;
                            dictionary_locale?: string | undefined;
                            dictionary_format?: string | undefined;
                            dictionary_path?: string | undefined;
                        } | undefined;
                        storage?: {
                            managed_schema?: string | undefined;
                        } | undefined;
                        tts_engine?: {
                            voices?: ({
                                voice_name?: string | undefined;
                                lang?: string | undefined;
                                gender?: string | undefined;
                                event_types?: (string | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                        } | undefined;
                        update_url?: string | undefined;
                        version_name?: string | undefined;
                        action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        page_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_specific_settings?: {
                            gecko?: {
                                id?: string | undefined;
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                                update_url?: string | undefined;
                            } | undefined;
                            gecko_android?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                            safari?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                        } | undefined;
                        permissions?: (chrome.runtime.ManifestPermissions | {
                            readonly [x: number]: string | undefined;
                            toString?: {} | undefined;
                            charAt?: {} | undefined;
                            charCodeAt?: {} | undefined;
                            concat?: {} | undefined;
                            indexOf?: {} | undefined;
                            lastIndexOf?: {} | undefined;
                            localeCompare?: {} | undefined;
                            match?: {} | undefined;
                            replace?: {} | undefined;
                            search?: {} | undefined;
                            slice?: {} | undefined;
                            split?: {} | undefined;
                            substring?: {} | undefined;
                            toLowerCase?: {} | undefined;
                            toLocaleLowerCase?: {} | undefined;
                            toUpperCase?: {} | undefined;
                            toLocaleUpperCase?: {} | undefined;
                            trim?: {} | undefined;
                            readonly length?: number | undefined;
                            substr?: {} | undefined;
                            valueOf?: {} | undefined;
                            codePointAt?: {} | undefined;
                            includes?: {} | undefined;
                            endsWith?: {} | undefined;
                            normalize?: {} | undefined;
                            repeat?: {} | undefined;
                            startsWith?: {} | undefined;
                            anchor?: {} | undefined;
                            big?: {} | undefined;
                            blink?: {} | undefined;
                            bold?: {} | undefined;
                            fixed?: {} | undefined;
                            fontcolor?: {} | undefined;
                            fontsize?: {} | undefined;
                            italics?: {} | undefined;
                            link?: {} | undefined;
                            small?: {} | undefined;
                            strike?: {} | undefined;
                            sub?: {} | undefined;
                            sup?: {} | undefined;
                            padStart?: {} | undefined;
                            padEnd?: {} | undefined;
                            trimEnd?: {} | undefined;
                            trimStart?: {} | undefined;
                            trimLeft?: {} | undefined;
                            trimRight?: {} | undefined;
                            matchAll?: {} | undefined;
                            [Symbol.iterator]?: {} | undefined;
                            at?: {} | undefined;
                        } | undefined)[] | undefined;
                        web_accessible_resources?: (string | undefined)[] | ({
                            resources?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                    } | {
                        then?: {} | undefined;
                        catch?: {} | undefined;
                        finally?: {} | undefined;
                        readonly [Symbol.toStringTag]?: string | undefined;
                    } | {} | undefined;
                    vite?: {} | undefined;
                    zip?: {
                        artifactTemplate?: string | undefined;
                        zipSources?: boolean | undefined;
                        sourcesTemplate?: string | undefined;
                        name?: string | undefined;
                        sourcesRoot?: string | undefined;
                        includeSources?: (string | undefined)[] | undefined;
                        excludeSources?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        downloadPackages?: (string | undefined)[] | undefined;
                        compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                    } | undefined;
                    root?: string | undefined;
                    mode?: string | undefined;
                    dev?: {
                        server?: {
                            port?: number | undefined;
                            hostname?: string | undefined;
                        } | undefined;
                        reloadCommand?: (string | false) | undefined;
                    } | undefined;
                    publicDir?: string | undefined;
                    experimental?: {} | undefined;
                    srcDir?: string | undefined;
                    entrypointsDir?: string | undefined;
                    modulesDir?: string | undefined;
                    filterEntrypoints?: (string | undefined)[] | undefined;
                    outDir?: string | undefined;
                    outDirTemplate?: string | undefined;
                    debug?: boolean | undefined;
                    imports?: false | {
                        imports?: ({
                            name?: string | undefined;
                            as?: import("unimport").ImportName | undefined;
                            with?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | undefined)[] | undefined;
                        presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                            imports?: (string | any | {
                                meta?: {
                                    [x: string]: any;
                                    description?: string | undefined;
                                    docsUrl?: string | undefined;
                                } | undefined;
                                name?: string | undefined;
                                type?: boolean | undefined;
                                as?: import("unimport").ImportName | undefined;
                                with?: {
                                    [x: string]: string | undefined;
                                } | undefined;
                                priority?: number | undefined;
                                disabled?: boolean | undefined;
                                dtsDisabled?: boolean | undefined;
                                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                typeFrom?: import("unimport").ModuleId | undefined;
                            } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | {
                            package?: string | undefined;
                            url?: string | undefined;
                            ignore?: (string | {
                                exec?: {} | undefined;
                                test?: {} | undefined;
                                readonly source?: string | undefined;
                                readonly global?: boolean | undefined;
                                readonly ignoreCase?: boolean | undefined;
                                readonly multiline?: boolean | undefined;
                                lastIndex?: number | undefined;
                                compile?: {} | undefined;
                                readonly flags?: string | undefined;
                                readonly sticky?: boolean | undefined;
                                readonly unicode?: boolean | undefined;
                                readonly dotAll?: boolean | undefined;
                                [Symbol.match]?: {} | undefined;
                                [Symbol.replace]?: {} | undefined;
                                [Symbol.search]?: {} | undefined;
                                [Symbol.split]?: {} | undefined;
                                [Symbol.matchAll]?: {} | undefined;
                            } | {} | undefined)[] | undefined;
                            cache?: boolean | undefined;
                        } | undefined)[] | undefined;
                        warn?: {} | undefined;
                        debugLog?: {} | undefined;
                        addons?: {
                            addons?: ({
                                name?: string | undefined;
                                transform?: {} | undefined;
                                declaration?: {} | undefined;
                                matchImports?: {} | undefined;
                                extendImports?: {} | undefined;
                                injectImportsResolved?: {} | undefined;
                                injectImportsStringified?: {} | undefined;
                            } | undefined)[] | undefined;
                            vueTemplate?: boolean | undefined;
                            vueDirectives?: true | {
                                isDirective?: {} | undefined;
                            } | undefined;
                        } | ({
                            name?: string | undefined;
                            transform?: {} | undefined;
                            declaration?: {} | undefined;
                            matchImports?: {} | undefined;
                            extendImports?: {} | undefined;
                            injectImportsResolved?: {} | undefined;
                            injectImportsStringified?: {} | undefined;
                        } | undefined)[] | undefined;
                        virtualImports?: (string | undefined)[] | undefined;
                        dirs?: (string | undefined)[] | undefined;
                        dirsScanOptions?: {
                            filePatterns?: (string | undefined)[] | undefined;
                            fileFilter?: {} | undefined;
                            types?: boolean | undefined;
                            cwd?: string | undefined;
                        } | undefined;
                        resolveId?: {} | undefined;
                        commentsDisable?: (string | undefined)[] | undefined;
                        commentsDebug?: (string | undefined)[] | undefined;
                        collectMeta?: boolean | undefined;
                        injectAtEnd?: boolean | undefined;
                        mergeExisting?: boolean | undefined;
                        parser?: ("acorn" | "regex") | undefined;
                        eslintrc?: {
                            enabled?: (false | true | "auto" | 8 | 9) | undefined;
                            filePath?: string | undefined;
                            globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                        } | undefined;
                    } | undefined;
                    browser?: import("../../../types").TargetBrowser | undefined;
                    manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                    logger?: {
                        debug?: {} | undefined;
                        log?: {} | undefined;
                        info?: {} | undefined;
                        warn?: {} | undefined;
                        error?: {} | undefined;
                        fatal?: {} | undefined;
                        success?: {} | undefined;
                        level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                            toString?: {} | undefined;
                            toFixed?: {} | undefined;
                            toExponential?: {} | undefined;
                            toPrecision?: {} | undefined;
                            valueOf?: {} | undefined;
                            toLocaleString?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    runner?: {
                        disabled?: boolean | undefined;
                        openConsole?: boolean | undefined;
                        openDevtools?: boolean | undefined;
                        binaries?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxProfile?: string | undefined;
                        chromiumProfile?: string | undefined;
                        chromiumPref?: {
                            [x: string]: any;
                        } | undefined;
                        chromiumPort?: number | undefined;
                        firefoxPrefs?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxArgs?: (string | undefined)[] | undefined;
                        chromiumArgs?: (string | undefined)[] | undefined;
                        startUrls?: (string | undefined)[] | undefined;
                        keepProfileChanges?: boolean | undefined;
                    } | undefined;
                    transformManifest?: {} | undefined;
                    analysis?: {
                        enabled?: boolean | undefined;
                        open?: boolean | undefined;
                        template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                        outputFile?: string | undefined;
                        keepArtifacts?: boolean | undefined;
                    } | undefined;
                    alias?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                    entrypointLoader?: ("vite-node" | "jiti") | undefined;
                    hooks?: {
                        'vite:build:extendConfig'?: {} | undefined;
                        'vite:devServer:extendConfig'?: {} | undefined;
                        ready?: {} | undefined;
                        'config:resolved'?: {} | undefined;
                        'prepare:types'?: {} | undefined;
                        'prepare:publicPaths'?: {} | undefined;
                        'build:before'?: {} | undefined;
                        'build:done'?: {} | undefined;
                        'build:manifestGenerated'?: {} | undefined;
                        'entrypoints:found'?: {} | undefined;
                        'entrypoints:resolved'?: {} | undefined;
                        'entrypoints:grouped'?: {} | undefined;
                        'build:publicAssets'?: {} | undefined;
                        'zip:start'?: {} | undefined;
                        'zip:extension:start'?: {} | undefined;
                        'zip:extension:done'?: {} | undefined;
                        'zip:sources:start'?: {} | undefined;
                        'zip:sources:done'?: {} | undefined;
                        'zip:done'?: {} | undefined;
                        'server:created'?: {} | undefined;
                        'server:started'?: {} | undefined;
                        'server:closed'?: {} | undefined;
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                    } | {
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                        ready?: {} | undefined;
                    } | undefined;
                    modules?: (string | undefined)[] | undefined;
                } | undefined;
                giget?: {
                    provider?: string | undefined;
                    force?: boolean | undefined;
                    forceClean?: boolean | undefined;
                    offline?: boolean | undefined;
                    preferOffline?: boolean | undefined;
                    providers?: {
                        [x: string]: {} | undefined;
                    } | undefined;
                    dir?: string | undefined;
                    registry?: (false | string) | undefined;
                    cwd?: string | undefined;
                    auth?: string | undefined;
                    install?: boolean | undefined;
                    silent?: boolean | undefined;
                } | undefined;
                install?: boolean | undefined;
                auth?: string | undefined;
            } | undefined;
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            cwd?: string | undefined;
            configFile?: string | undefined;
        } | undefined)[] | undefined;
        cwd?: string | undefined;
        sourceOptions?: {
            [x: string]: any;
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            overrides?: {
                manifest?: {
                    [x: string]: any;
                    content_scripts?: ({
                        matches?: (string | undefined)[] | undefined;
                        exclude_matches?: (string | undefined)[] | undefined;
                        css?: (string | undefined)[] | undefined;
                        js?: (string | undefined)[] | undefined;
                        run_at?: string | undefined;
                        all_frames?: boolean | undefined;
                        match_about_blank?: boolean | undefined;
                        include_globs?: (string | undefined)[] | undefined;
                        exclude_globs?: (string | undefined)[] | undefined;
                        world?: "ISOLATED" | "MAIN" | undefined;
                    } | undefined)[] | undefined;
                    content_security_policy?: {
                        extension_pages?: string | undefined;
                        sandbox?: string | undefined;
                    } | undefined;
                    host_permissions?: (string | undefined)[] | undefined;
                    optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                    optional_host_permissions?: (string | undefined)[] | undefined;
                    name?: string | undefined;
                    version?: string | undefined;
                    default_locale?: string | undefined;
                    description?: string | undefined;
                    icons?: {
                        [x: number]: string | undefined;
                    } | undefined;
                    author?: {
                        email?: string | undefined;
                    } | undefined;
                    background_page?: string | undefined;
                    chrome_settings_overrides?: {
                        homepage?: string | undefined;
                        search_provider?: {
                            name?: string | undefined;
                            keyword?: string | undefined;
                            favicon_url?: string | undefined;
                            search_url?: string | undefined;
                            encoding?: string | undefined;
                            suggest_url?: string | undefined;
                            instant_url?: string | undefined;
                            image_url?: string | undefined;
                            search_url_post_params?: string | undefined;
                            suggest_url_post_params?: string | undefined;
                            instant_url_post_params?: string | undefined;
                            image_url_post_params?: string | undefined;
                            alternate_urls?: (string | undefined)[] | undefined;
                            prepopulated_id?: number | undefined;
                            is_default?: boolean | undefined;
                        } | undefined;
                        startup_pages?: (string | undefined)[] | undefined;
                    } | undefined;
                    chrome_ui_overrides?: {
                        bookmarks_ui?: {
                            remove_bookmark_shortcut?: boolean | undefined;
                            remove_button?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    commands?: {
                        [x: string]: {
                            suggested_key?: {
                                default?: string | undefined;
                                windows?: string | undefined;
                                mac?: string | undefined;
                                chromeos?: string | undefined;
                                linux?: string | undefined;
                            } | undefined;
                            description?: string | undefined;
                            global?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    content_capabilities?: {
                        matches?: (string | undefined)[] | undefined;
                        permissions?: (string | undefined)[] | undefined;
                    } | undefined;
                    converted_from_user_script?: boolean | undefined;
                    current_locale?: string | undefined;
                    event_rules?: ({
                        event?: string | undefined;
                        actions?: ({
                            type?: string | undefined;
                        } | undefined)[] | undefined;
                        conditions?: ({
                            pageUrl?: {
                                hostContains?: string | undefined;
                                hostEquals?: string | undefined;
                                hostPrefix?: string | undefined;
                                hostSuffix?: string | undefined;
                                pathContains?: string | undefined;
                                pathEquals?: string | undefined;
                                pathPrefix?: string | undefined;
                                pathSuffix?: string | undefined;
                                queryContains?: string | undefined;
                                queryEquals?: string | undefined;
                                queryPrefix?: string | undefined;
                                querySuffix?: string | undefined;
                                urlContains?: string | undefined;
                                urlEquals?: string | undefined;
                                urlMatches?: string | undefined;
                                originAndPathMatches?: string | undefined;
                                urlPrefix?: string | undefined;
                                urlSuffix?: string | undefined;
                                schemes?: (string | undefined)[] | undefined;
                                ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                            } | undefined;
                            css?: (string | undefined)[] | undefined;
                            isBookmarked?: boolean | undefined;
                        } | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                    externally_connectable?: {
                        ids?: (string | undefined)[] | undefined;
                        matches?: (string | undefined)[] | undefined;
                        accepts_tls_channel_id?: boolean | undefined;
                    } | undefined;
                    file_browser_handlers?: ({
                        id?: string | undefined;
                        default_title?: string | undefined;
                        file_filters?: (string | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                    file_system_provider_capabilities?: {
                        configurable?: boolean | undefined;
                        watchable?: boolean | undefined;
                        multiple_mounts?: boolean | undefined;
                        source?: string | undefined;
                    } | undefined;
                    homepage_url?: string | undefined;
                    import?: ({
                        id?: string | undefined;
                        minimum_version?: string | undefined;
                    } | undefined)[] | undefined;
                    export?: {
                        whitelist?: (string | undefined)[] | undefined;
                    } | undefined;
                    incognito?: string | undefined;
                    input_components?: ({
                        name?: string | undefined;
                        type?: string | undefined;
                        id?: string | undefined;
                        description?: string | undefined;
                        language?: string | (string | undefined)[] | undefined;
                        layouts?: (string | undefined)[] | undefined;
                        indicator?: string | undefined;
                    } | undefined)[] | undefined;
                    key?: string | undefined;
                    minimum_chrome_version?: string | undefined;
                    nacl_modules?: ({
                        path?: string | undefined;
                        mime_type?: string | undefined;
                    } | undefined)[] | undefined;
                    oauth2?: {
                        client_id?: string | undefined;
                        scopes?: (string | undefined)[] | undefined;
                    } | undefined;
                    offline_enabled?: boolean | undefined;
                    omnibox?: {
                        keyword?: string | undefined;
                    } | undefined;
                    platforms?: ({
                        nacl_arch?: string | undefined;
                        sub_package_path?: string | undefined;
                    } | undefined)[] | undefined;
                    plugins?: ({
                        path?: string | undefined;
                    } | undefined)[] | undefined;
                    requirements?: {
                        "3D"?: {
                            features?: (string | undefined)[] | undefined;
                        } | undefined;
                        plugins?: {
                            npapi?: boolean | undefined;
                        } | undefined;
                    } | undefined;
                    short_name?: string | undefined;
                    spellcheck?: {
                        dictionary_language?: string | undefined;
                        dictionary_locale?: string | undefined;
                        dictionary_format?: string | undefined;
                        dictionary_path?: string | undefined;
                    } | undefined;
                    storage?: {
                        managed_schema?: string | undefined;
                    } | undefined;
                    tts_engine?: {
                        voices?: ({
                            voice_name?: string | undefined;
                            lang?: string | undefined;
                            gender?: string | undefined;
                            event_types?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                    } | undefined;
                    update_url?: string | undefined;
                    version_name?: string | undefined;
                    action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    browser_action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    page_action?: {
                        default_icon?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        default_title?: string | undefined;
                        default_popup?: string | undefined;
                        browser_style?: boolean | undefined;
                    } | undefined;
                    browser_specific_settings?: {
                        gecko?: {
                            id?: string | undefined;
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                            update_url?: string | undefined;
                        } | undefined;
                        gecko_android?: {
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                        } | undefined;
                        safari?: {
                            strict_min_version?: string | undefined;
                            strict_max_version?: string | undefined;
                        } | undefined;
                    } | undefined;
                    permissions?: (chrome.runtime.ManifestPermissions | {
                        readonly [x: number]: string | undefined;
                        toString?: {} | undefined;
                        charAt?: {} | undefined;
                        charCodeAt?: {} | undefined;
                        concat?: {} | undefined;
                        indexOf?: {} | undefined;
                        lastIndexOf?: {} | undefined;
                        localeCompare?: {} | undefined;
                        match?: {} | undefined;
                        replace?: {} | undefined;
                        search?: {} | undefined;
                        slice?: {} | undefined;
                        split?: {} | undefined;
                        substring?: {} | undefined;
                        toLowerCase?: {} | undefined;
                        toLocaleLowerCase?: {} | undefined;
                        toUpperCase?: {} | undefined;
                        toLocaleUpperCase?: {} | undefined;
                        trim?: {} | undefined;
                        readonly length?: number | undefined;
                        substr?: {} | undefined;
                        valueOf?: {} | undefined;
                        codePointAt?: {} | undefined;
                        includes?: {} | undefined;
                        endsWith?: {} | undefined;
                        normalize?: {} | undefined;
                        repeat?: {} | undefined;
                        startsWith?: {} | undefined;
                        anchor?: {} | undefined;
                        big?: {} | undefined;
                        blink?: {} | undefined;
                        bold?: {} | undefined;
                        fixed?: {} | undefined;
                        fontcolor?: {} | undefined;
                        fontsize?: {} | undefined;
                        italics?: {} | undefined;
                        link?: {} | undefined;
                        small?: {} | undefined;
                        strike?: {} | undefined;
                        sub?: {} | undefined;
                        sup?: {} | undefined;
                        padStart?: {} | undefined;
                        padEnd?: {} | undefined;
                        trimEnd?: {} | undefined;
                        trimStart?: {} | undefined;
                        trimLeft?: {} | undefined;
                        trimRight?: {} | undefined;
                        matchAll?: {} | undefined;
                        [Symbol.iterator]?: {} | undefined;
                        at?: {} | undefined;
                    } | undefined)[] | undefined;
                    web_accessible_resources?: (string | undefined)[] | ({
                        resources?: (string | undefined)[] | undefined;
                        matches?: (string | undefined)[] | undefined;
                    } | undefined)[] | undefined;
                } | {
                    then?: {} | undefined;
                    catch?: {} | undefined;
                    finally?: {} | undefined;
                    readonly [Symbol.toStringTag]?: string | undefined;
                } | {} | undefined;
                vite?: {} | undefined;
                zip?: {
                    artifactTemplate?: string | undefined;
                    zipSources?: boolean | undefined;
                    sourcesTemplate?: string | undefined;
                    name?: string | undefined;
                    sourcesRoot?: string | undefined;
                    includeSources?: (string | undefined)[] | undefined;
                    excludeSources?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    downloadPackages?: (string | undefined)[] | undefined;
                    compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                } | undefined;
                root?: string | undefined;
                mode?: string | undefined;
                dev?: {
                    server?: {
                        port?: number | undefined;
                        hostname?: string | undefined;
                    } | undefined;
                    reloadCommand?: (string | false) | undefined;
                } | undefined;
                publicDir?: string | undefined;
                experimental?: {} | undefined;
                srcDir?: string | undefined;
                entrypointsDir?: string | undefined;
                modulesDir?: string | undefined;
                filterEntrypoints?: (string | undefined)[] | undefined;
                outDir?: string | undefined;
                outDirTemplate?: string | undefined;
                debug?: boolean | undefined;
                imports?: false | {
                    imports?: ({
                        name?: string | undefined;
                        as?: import("unimport").ImportName | undefined;
                        with?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        from?: string | undefined;
                        priority?: number | undefined;
                        disabled?: boolean | undefined;
                        dtsDisabled?: boolean | undefined;
                        declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                        meta?: {
                            [x: string]: any;
                            description?: string | undefined;
                            docsUrl?: string | undefined;
                        } | undefined;
                        type?: boolean | undefined;
                        typeFrom?: import("unimport").ModuleId | undefined;
                    } | undefined)[] | undefined;
                    presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                        imports?: (string | any | {
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            name?: string | undefined;
                            type?: boolean | undefined;
                            as?: import("unimport").ImportName | undefined;
                            with?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                        from?: string | undefined;
                        priority?: number | undefined;
                        disabled?: boolean | undefined;
                        dtsDisabled?: boolean | undefined;
                        declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                        meta?: {
                            [x: string]: any;
                            description?: string | undefined;
                            docsUrl?: string | undefined;
                        } | undefined;
                        type?: boolean | undefined;
                        typeFrom?: import("unimport").ModuleId | undefined;
                    } | {
                        package?: string | undefined;
                        url?: string | undefined;
                        ignore?: (string | {
                            exec?: {} | undefined;
                            test?: {} | undefined;
                            readonly source?: string | undefined;
                            readonly global?: boolean | undefined;
                            readonly ignoreCase?: boolean | undefined;
                            readonly multiline?: boolean | undefined;
                            lastIndex?: number | undefined;
                            compile?: {} | undefined;
                            readonly flags?: string | undefined;
                            readonly sticky?: boolean | undefined;
                            readonly unicode?: boolean | undefined;
                            readonly dotAll?: boolean | undefined;
                            [Symbol.match]?: {} | undefined;
                            [Symbol.replace]?: {} | undefined;
                            [Symbol.search]?: {} | undefined;
                            [Symbol.split]?: {} | undefined;
                            [Symbol.matchAll]?: {} | undefined;
                        } | {} | undefined)[] | undefined;
                        cache?: boolean | undefined;
                    } | undefined)[] | undefined;
                    warn?: {} | undefined;
                    debugLog?: {} | undefined;
                    addons?: {
                        addons?: ({
                            name?: string | undefined;
                            transform?: {} | undefined;
                            declaration?: {} | undefined;
                            matchImports?: {} | undefined;
                            extendImports?: {} | undefined;
                            injectImportsResolved?: {} | undefined;
                            injectImportsStringified?: {} | undefined;
                        } | undefined)[] | undefined;
                        vueTemplate?: boolean | undefined;
                        vueDirectives?: true | {
                            isDirective?: {} | undefined;
                        } | undefined;
                    } | ({
                        name?: string | undefined;
                        transform?: {} | undefined;
                        declaration?: {} | undefined;
                        matchImports?: {} | undefined;
                        extendImports?: {} | undefined;
                        injectImportsResolved?: {} | undefined;
                        injectImportsStringified?: {} | undefined;
                    } | undefined)[] | undefined;
                    virtualImports?: (string | undefined)[] | undefined;
                    dirs?: (string | undefined)[] | undefined;
                    dirsScanOptions?: {
                        filePatterns?: (string | undefined)[] | undefined;
                        fileFilter?: {} | undefined;
                        types?: boolean | undefined;
                        cwd?: string | undefined;
                    } | undefined;
                    resolveId?: {} | undefined;
                    commentsDisable?: (string | undefined)[] | undefined;
                    commentsDebug?: (string | undefined)[] | undefined;
                    collectMeta?: boolean | undefined;
                    injectAtEnd?: boolean | undefined;
                    mergeExisting?: boolean | undefined;
                    parser?: ("acorn" | "regex") | undefined;
                    eslintrc?: {
                        enabled?: (false | true | "auto" | 8 | 9) | undefined;
                        filePath?: string | undefined;
                        globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                    } | undefined;
                } | undefined;
                browser?: import("../../../types").TargetBrowser | undefined;
                manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                logger?: {
                    debug?: {} | undefined;
                    log?: {} | undefined;
                    info?: {} | undefined;
                    warn?: {} | undefined;
                    error?: {} | undefined;
                    fatal?: {} | undefined;
                    success?: {} | undefined;
                    level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                        toString?: {} | undefined;
                        toFixed?: {} | undefined;
                        toExponential?: {} | undefined;
                        toPrecision?: {} | undefined;
                        valueOf?: {} | undefined;
                        toLocaleString?: {} | undefined;
                    } | undefined;
                } | undefined;
                runner?: {
                    disabled?: boolean | undefined;
                    openConsole?: boolean | undefined;
                    openDevtools?: boolean | undefined;
                    binaries?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxProfile?: string | undefined;
                    chromiumProfile?: string | undefined;
                    chromiumPref?: {
                        [x: string]: any;
                    } | undefined;
                    chromiumPort?: number | undefined;
                    firefoxPrefs?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxArgs?: (string | undefined)[] | undefined;
                    chromiumArgs?: (string | undefined)[] | undefined;
                    startUrls?: (string | undefined)[] | undefined;
                    keepProfileChanges?: boolean | undefined;
                } | undefined;
                transformManifest?: {} | undefined;
                analysis?: {
                    enabled?: boolean | undefined;
                    open?: boolean | undefined;
                    template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                    outputFile?: string | undefined;
                    keepArtifacts?: boolean | undefined;
                } | undefined;
                alias?: {
                    [x: string]: string | undefined;
                } | undefined;
                extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                entrypointLoader?: ("vite-node" | "jiti") | undefined;
                hooks?: {
                    'vite:build:extendConfig'?: {} | undefined;
                    'vite:devServer:extendConfig'?: {} | undefined;
                    ready?: {} | undefined;
                    'config:resolved'?: {} | undefined;
                    'prepare:types'?: {} | undefined;
                    'prepare:publicPaths'?: {} | undefined;
                    'build:before'?: {} | undefined;
                    'build:done'?: {} | undefined;
                    'build:manifestGenerated'?: {} | undefined;
                    'entrypoints:found'?: {} | undefined;
                    'entrypoints:resolved'?: {} | undefined;
                    'entrypoints:grouped'?: {} | undefined;
                    'build:publicAssets'?: {} | undefined;
                    'zip:start'?: {} | undefined;
                    'zip:extension:start'?: {} | undefined;
                    'zip:extension:done'?: {} | undefined;
                    'zip:sources:start'?: {} | undefined;
                    'zip:sources:done'?: {} | undefined;
                    'zip:done'?: {} | undefined;
                    'server:created'?: {} | undefined;
                    'server:started'?: {} | undefined;
                    'server:closed'?: {} | undefined;
                    vite?: {
                        "build:extendConfig"?: {} | undefined;
                        "devServer:extendConfig"?: {} | undefined;
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | {
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    config?: {
                        resolved?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                    } | undefined;
                    prepare?: {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | undefined;
                    build?: {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | undefined;
                    entrypoints?: {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | undefined;
                    zip?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                        "extension:start"?: {} | undefined;
                        "extension:done"?: {} | undefined;
                        "sources:start"?: {} | undefined;
                        "sources:done"?: {} | undefined;
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                    } | {
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    server?: {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | undefined;
                } | {
                    vite?: {
                        "build:extendConfig"?: {} | undefined;
                        "devServer:extendConfig"?: {} | undefined;
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | {
                        build?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                        devServer?: {
                            extendConfig?: {} | undefined;
                        } | {
                            extendConfig?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    config?: {
                        resolved?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                    } | undefined;
                    prepare?: {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | {
                        types?: {} | undefined;
                        publicPaths?: {} | undefined;
                    } | undefined;
                    build?: {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | {
                        before?: {} | undefined;
                        done?: {} | undefined;
                        manifestGenerated?: {} | undefined;
                        publicAssets?: {} | undefined;
                    } | undefined;
                    entrypoints?: {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | {
                        resolved?: {} | undefined;
                        found?: {} | undefined;
                        grouped?: {} | undefined;
                    } | undefined;
                    zip?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                        "extension:start"?: {} | undefined;
                        "extension:done"?: {} | undefined;
                        "sources:start"?: {} | undefined;
                        "sources:done"?: {} | undefined;
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                    } | {
                        extension?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        sources?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | {
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    server?: {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | {
                        closed?: {} | undefined;
                        created?: {} | undefined;
                        started?: {} | undefined;
                    } | undefined;
                    ready?: {} | undefined;
                } | undefined;
                modules?: (string | undefined)[] | undefined;
            } | undefined;
            giget?: {
                provider?: string | undefined;
                force?: boolean | undefined;
                forceClean?: boolean | undefined;
                offline?: boolean | undefined;
                preferOffline?: boolean | undefined;
                providers?: {
                    [x: string]: {} | undefined;
                } | undefined;
                dir?: string | undefined;
                registry?: (false | string) | undefined;
                cwd?: string | undefined;
                auth?: string | undefined;
                install?: boolean | undefined;
                silent?: boolean | undefined;
            } | undefined;
            install?: boolean | undefined;
            auth?: string | undefined;
        } | undefined;
    } | undefined;
    alias?: {
        [x: string]: string | undefined;
    } | undefined;
    extensionApi?: "webextension-polyfill" | "chrome" | undefined;
    entrypointLoader?: "vite-node" | "jiti" | undefined;
    experimental?: {} | undefined;
    dev?: {
        server?: {
            port?: number | undefined;
            hostname?: string | undefined;
            watchDebounce?: number | undefined;
        } | undefined;
        reloadCommand?: string | false | undefined;
    } | undefined;
    hooks?: {
        'vite:build:extendConfig'?: {} | undefined;
        'vite:devServer:extendConfig'?: {} | undefined;
        ready?: {} | undefined;
        'config:resolved'?: {} | undefined;
        'prepare:types'?: {} | undefined;
        'prepare:publicPaths'?: {} | undefined;
        'build:before'?: {} | undefined;
        'build:done'?: {} | undefined;
        'build:manifestGenerated'?: {} | undefined;
        'entrypoints:found'?: {} | undefined;
        'entrypoints:resolved'?: {} | undefined;
        'entrypoints:grouped'?: {} | undefined;
        'build:publicAssets'?: {} | undefined;
        'zip:start'?: {} | undefined;
        'zip:extension:start'?: {} | undefined;
        'zip:extension:done'?: {} | undefined;
        'zip:sources:start'?: {} | undefined;
        'zip:sources:done'?: {} | undefined;
        'zip:done'?: {} | undefined;
        'server:created'?: {} | undefined;
        'server:started'?: {} | undefined;
        'server:closed'?: {} | undefined;
        vite?: {
            "build:extendConfig"?: {} | undefined;
            "devServer:extendConfig"?: {} | undefined;
            build?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
            devServer?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
        } | {
            build?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
            devServer?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
        } | undefined;
        config?: {
            resolved?: {} | undefined;
        } | {
            resolved?: {} | undefined;
        } | undefined;
        prepare?: {
            types?: {} | undefined;
            publicPaths?: {} | undefined;
        } | {
            types?: {} | undefined;
            publicPaths?: {} | undefined;
        } | undefined;
        build?: {
            before?: {} | undefined;
            done?: {} | undefined;
            manifestGenerated?: {} | undefined;
            publicAssets?: {} | undefined;
        } | {
            before?: {} | undefined;
            done?: {} | undefined;
            manifestGenerated?: {} | undefined;
            publicAssets?: {} | undefined;
        } | undefined;
        entrypoints?: {
            resolved?: {} | undefined;
            found?: {} | undefined;
            grouped?: {} | undefined;
        } | {
            resolved?: {} | undefined;
            found?: {} | undefined;
            grouped?: {} | undefined;
        } | undefined;
        zip?: {
            done?: {} | undefined;
            start?: {} | undefined;
            "extension:start"?: {} | undefined;
            "extension:done"?: {} | undefined;
            "sources:start"?: {} | undefined;
            "sources:done"?: {} | undefined;
            extension?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            sources?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
        } | {
            extension?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            sources?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            done?: {} | undefined;
            start?: {} | undefined;
        } | undefined;
        server?: {
            closed?: {} | undefined;
            created?: {} | undefined;
            started?: {} | undefined;
        } | {
            closed?: {} | undefined;
            created?: {} | undefined;
            started?: {} | undefined;
        } | undefined;
    } | {
        vite?: {
            "build:extendConfig"?: {} | undefined;
            "devServer:extendConfig"?: {} | undefined;
            build?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
            devServer?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
        } | {
            build?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
            devServer?: {
                extendConfig?: {} | undefined;
            } | {
                extendConfig?: {} | undefined;
            } | undefined;
        } | undefined;
        config?: {
            resolved?: {} | undefined;
        } | {
            resolved?: {} | undefined;
        } | undefined;
        prepare?: {
            types?: {} | undefined;
            publicPaths?: {} | undefined;
        } | {
            types?: {} | undefined;
            publicPaths?: {} | undefined;
        } | undefined;
        build?: {
            before?: {} | undefined;
            done?: {} | undefined;
            manifestGenerated?: {} | undefined;
            publicAssets?: {} | undefined;
        } | {
            before?: {} | undefined;
            done?: {} | undefined;
            manifestGenerated?: {} | undefined;
            publicAssets?: {} | undefined;
        } | undefined;
        entrypoints?: {
            resolved?: {} | undefined;
            found?: {} | undefined;
            grouped?: {} | undefined;
        } | {
            resolved?: {} | undefined;
            found?: {} | undefined;
            grouped?: {} | undefined;
        } | undefined;
        zip?: {
            done?: {} | undefined;
            start?: {} | undefined;
            "extension:start"?: {} | undefined;
            "extension:done"?: {} | undefined;
            "sources:start"?: {} | undefined;
            "sources:done"?: {} | undefined;
            extension?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            sources?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
        } | {
            extension?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            sources?: {
                done?: {} | undefined;
                start?: {} | undefined;
            } | {
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            done?: {} | undefined;
            start?: {} | undefined;
        } | undefined;
        server?: {
            closed?: {} | undefined;
            created?: {} | undefined;
            started?: {} | undefined;
        } | {
            closed?: {} | undefined;
            created?: {} | undefined;
            started?: {} | undefined;
        } | undefined;
        ready?: {} | undefined;
    } | undefined;
    builtinModules?: ({
        name?: string | undefined;
        configKey?: string | undefined;
        imports?: ({
            name?: string | undefined;
            as?: import("unimport").ImportName | undefined;
            with?: {
                [x: string]: string | undefined;
            } | undefined;
            from?: string | undefined;
            priority?: number | undefined;
            disabled?: boolean | undefined;
            dtsDisabled?: boolean | undefined;
            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
            meta?: {
                [x: string]: any;
                description?: string | undefined;
                docsUrl?: string | undefined;
            } | undefined;
            type?: boolean | undefined;
            typeFrom?: import("unimport").ModuleId | undefined;
        } | undefined)[] | undefined;
        hooks?: {
            'vite:build:extendConfig'?: {} | undefined;
            'vite:devServer:extendConfig'?: {} | undefined;
            ready?: {} | undefined;
            'config:resolved'?: {} | undefined;
            'prepare:types'?: {} | undefined;
            'prepare:publicPaths'?: {} | undefined;
            'build:before'?: {} | undefined;
            'build:done'?: {} | undefined;
            'build:manifestGenerated'?: {} | undefined;
            'entrypoints:found'?: {} | undefined;
            'entrypoints:resolved'?: {} | undefined;
            'entrypoints:grouped'?: {} | undefined;
            'build:publicAssets'?: {} | undefined;
            'zip:start'?: {} | undefined;
            'zip:extension:start'?: {} | undefined;
            'zip:extension:done'?: {} | undefined;
            'zip:sources:start'?: {} | undefined;
            'zip:sources:done'?: {} | undefined;
            'zip:done'?: {} | undefined;
            'server:created'?: {} | undefined;
            'server:started'?: {} | undefined;
            'server:closed'?: {} | undefined;
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
        } | {
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
            ready?: {} | undefined;
        } | undefined;
        setup?: {} | undefined;
    } | undefined)[] | undefined;
    userModules?: ({
        type?: "local" | "node_module" | undefined;
        id?: string | undefined;
        name?: string | undefined;
        configKey?: string | undefined;
        imports?: ({
            name?: string | undefined;
            as?: import("unimport").ImportName | undefined;
            with?: {
                [x: string]: string | undefined;
            } | undefined;
            from?: string | undefined;
            priority?: number | undefined;
            disabled?: boolean | undefined;
            dtsDisabled?: boolean | undefined;
            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
            meta?: {
                [x: string]: any;
                description?: string | undefined;
                docsUrl?: string | undefined;
            } | undefined;
            type?: boolean | undefined;
            typeFrom?: import("unimport").ModuleId | undefined;
        } | undefined)[] | undefined;
        hooks?: {
            'vite:build:extendConfig'?: {} | undefined;
            'vite:devServer:extendConfig'?: {} | undefined;
            ready?: {} | undefined;
            'config:resolved'?: {} | undefined;
            'prepare:types'?: {} | undefined;
            'prepare:publicPaths'?: {} | undefined;
            'build:before'?: {} | undefined;
            'build:done'?: {} | undefined;
            'build:manifestGenerated'?: {} | undefined;
            'entrypoints:found'?: {} | undefined;
            'entrypoints:resolved'?: {} | undefined;
            'entrypoints:grouped'?: {} | undefined;
            'build:publicAssets'?: {} | undefined;
            'zip:start'?: {} | undefined;
            'zip:extension:start'?: {} | undefined;
            'zip:extension:done'?: {} | undefined;
            'zip:sources:start'?: {} | undefined;
            'zip:sources:done'?: {} | undefined;
            'zip:done'?: {} | undefined;
            'server:created'?: {} | undefined;
            'server:started'?: {} | undefined;
            'server:closed'?: {} | undefined;
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
        } | {
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
            ready?: {} | undefined;
        } | undefined;
        setup?: {} | undefined;
    } | undefined)[] | undefined;
    plugins?: (string | undefined)[] | undefined;
} | undefined) => ResolvedConfig;
export declare const fakeWxt: (overrides?: {
    config?: {
        vite?: {} | undefined;
        root?: string | undefined;
        srcDir?: string | undefined;
        publicDir?: string | undefined;
        wxtDir?: string | undefined;
        typesDir?: string | undefined;
        entrypointsDir?: string | undefined;
        modulesDir?: string | undefined;
        filterEntrypoints?: {
            add?: {} | undefined;
            clear?: {} | undefined;
            delete?: {} | undefined;
            forEach?: {} | undefined;
            has?: {} | undefined;
            readonly size?: number | undefined;
            entries?: {} | undefined;
            keys?: {} | undefined;
            values?: {} | undefined;
            [Symbol.iterator]?: {} | undefined;
            readonly [Symbol.toStringTag]?: string | undefined;
        } | undefined;
        outBaseDir?: string | undefined;
        outDir?: string | undefined;
        debug?: boolean | undefined;
        wxtModuleDir?: string | undefined;
        mode?: string | undefined;
        command?: import("../../../types").WxtCommand | undefined;
        browser?: string | undefined;
        manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
        env?: {
            mode?: string | undefined;
            command?: import("../../../types").WxtCommand | undefined;
            browser?: string | undefined;
            manifestVersion?: 2 | 3 | undefined;
        } | undefined;
        logger?: {
            debug?: {} | undefined;
            log?: {} | undefined;
            info?: {} | undefined;
            warn?: {} | undefined;
            error?: {} | undefined;
            fatal?: {} | undefined;
            success?: {} | undefined;
            level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                toString?: {} | undefined;
                toFixed?: {} | undefined;
                toExponential?: {} | undefined;
                toPrecision?: {} | undefined;
                valueOf?: {} | undefined;
                toLocaleString?: {} | undefined;
            } | undefined;
        } | undefined;
        imports?: false | {
            imports?: ({
                name?: string | undefined;
                as?: import("unimport").ImportName | undefined;
                with?: {
                    [x: string]: string | undefined;
                } | undefined;
                from?: string | undefined;
                priority?: number | undefined;
                disabled?: boolean | undefined;
                dtsDisabled?: boolean | undefined;
                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                meta?: {
                    [x: string]: any;
                    description?: string | undefined;
                    docsUrl?: string | undefined;
                } | undefined;
                type?: boolean | undefined;
                typeFrom?: import("unimport").ModuleId | undefined;
            } | undefined)[] | undefined;
            presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                imports?: (string | any | {
                    meta?: {
                        [x: string]: any;
                        description?: string | undefined;
                        docsUrl?: string | undefined;
                    } | undefined;
                    name?: string | undefined;
                    type?: boolean | undefined;
                    as?: import("unimport").ImportName | undefined;
                    with?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    priority?: number | undefined;
                    disabled?: boolean | undefined;
                    dtsDisabled?: boolean | undefined;
                    declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                    typeFrom?: import("unimport").ModuleId | undefined;
                } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                from?: string | undefined;
                priority?: number | undefined;
                disabled?: boolean | undefined;
                dtsDisabled?: boolean | undefined;
                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                meta?: {
                    [x: string]: any;
                    description?: string | undefined;
                    docsUrl?: string | undefined;
                } | undefined;
                type?: boolean | undefined;
                typeFrom?: import("unimport").ModuleId | undefined;
            } | {
                package?: string | undefined;
                url?: string | undefined;
                ignore?: (string | {
                    exec?: {} | undefined;
                    test?: {} | undefined;
                    readonly source?: string | undefined;
                    readonly global?: boolean | undefined;
                    readonly ignoreCase?: boolean | undefined;
                    readonly multiline?: boolean | undefined;
                    lastIndex?: number | undefined;
                    compile?: {} | undefined;
                    readonly flags?: string | undefined;
                    readonly sticky?: boolean | undefined;
                    readonly unicode?: boolean | undefined;
                    readonly dotAll?: boolean | undefined;
                    [Symbol.match]?: {} | undefined;
                    [Symbol.replace]?: {} | undefined;
                    [Symbol.search]?: {} | undefined;
                    [Symbol.split]?: {} | undefined;
                    [Symbol.matchAll]?: {} | undefined;
                } | {} | undefined)[] | undefined;
                cache?: boolean | undefined;
            } | undefined)[] | undefined;
            warn?: {} | undefined;
            debugLog?: {} | undefined;
            addons?: {
                addons?: ({
                    name?: string | undefined;
                    transform?: {} | undefined;
                    declaration?: {} | undefined;
                    matchImports?: {} | undefined;
                    extendImports?: {} | undefined;
                    injectImportsResolved?: {} | undefined;
                    injectImportsStringified?: {} | undefined;
                } | undefined)[] | undefined;
                vueTemplate?: boolean | undefined;
                vueDirectives?: true | {
                    isDirective?: {} | undefined;
                } | undefined;
            } | ({
                name?: string | undefined;
                transform?: {} | undefined;
                declaration?: {} | undefined;
                matchImports?: {} | undefined;
                extendImports?: {} | undefined;
                injectImportsResolved?: {} | undefined;
                injectImportsStringified?: {} | undefined;
            } | undefined)[] | undefined;
            virtualImports?: (string | undefined)[] | undefined;
            dirs?: (string | undefined)[] | undefined;
            dirsScanOptions?: {
                filePatterns?: (string | undefined)[] | undefined;
                fileFilter?: {} | undefined;
                types?: boolean | undefined;
                cwd?: string | undefined;
            } | undefined;
            resolveId?: {} | undefined;
            commentsDisable?: (string | undefined)[] | undefined;
            commentsDebug?: (string | undefined)[] | undefined;
            collectMeta?: boolean | undefined;
            injectAtEnd?: boolean | undefined;
            mergeExisting?: boolean | undefined;
            parser?: ("acorn" | "regex") | undefined;
            eslintrc?: {
                enabled?: false | 9 | 8 | undefined;
                filePath?: string | undefined;
                globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
            } | undefined;
        } | undefined;
        manifest?: {
            [x: string]: any;
            content_scripts?: ({
                matches?: (string | undefined)[] | undefined;
                exclude_matches?: (string | undefined)[] | undefined;
                css?: (string | undefined)[] | undefined;
                js?: (string | undefined)[] | undefined;
                run_at?: string | undefined;
                all_frames?: boolean | undefined;
                match_about_blank?: boolean | undefined;
                include_globs?: (string | undefined)[] | undefined;
                exclude_globs?: (string | undefined)[] | undefined;
                world?: "ISOLATED" | "MAIN" | undefined;
            } | undefined)[] | undefined;
            content_security_policy?: {
                extension_pages?: string | undefined;
                sandbox?: string | undefined;
            } | undefined;
            host_permissions?: (string | undefined)[] | undefined;
            optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
            optional_host_permissions?: (string | undefined)[] | undefined;
            name?: string | undefined;
            version?: string | undefined;
            default_locale?: string | undefined;
            description?: string | undefined;
            icons?: {
                [x: number]: string | undefined;
            } | undefined;
            author?: {
                email?: string | undefined;
            } | undefined;
            background_page?: string | undefined;
            chrome_settings_overrides?: {
                homepage?: string | undefined;
                search_provider?: {
                    name?: string | undefined;
                    keyword?: string | undefined;
                    favicon_url?: string | undefined;
                    search_url?: string | undefined;
                    encoding?: string | undefined;
                    suggest_url?: string | undefined;
                    instant_url?: string | undefined;
                    image_url?: string | undefined;
                    search_url_post_params?: string | undefined;
                    suggest_url_post_params?: string | undefined;
                    instant_url_post_params?: string | undefined;
                    image_url_post_params?: string | undefined;
                    alternate_urls?: (string | undefined)[] | undefined;
                    prepopulated_id?: number | undefined;
                    is_default?: boolean | undefined;
                } | undefined;
                startup_pages?: (string | undefined)[] | undefined;
            } | undefined;
            chrome_ui_overrides?: {
                bookmarks_ui?: {
                    remove_bookmark_shortcut?: boolean | undefined;
                    remove_button?: boolean | undefined;
                } | undefined;
            } | undefined;
            commands?: {
                [x: string]: {
                    suggested_key?: {
                        default?: string | undefined;
                        windows?: string | undefined;
                        mac?: string | undefined;
                        chromeos?: string | undefined;
                        linux?: string | undefined;
                    } | undefined;
                    description?: string | undefined;
                    global?: boolean | undefined;
                } | undefined;
            } | undefined;
            content_capabilities?: {
                matches?: (string | undefined)[] | undefined;
                permissions?: (string | undefined)[] | undefined;
            } | undefined;
            converted_from_user_script?: boolean | undefined;
            current_locale?: string | undefined;
            event_rules?: ({
                event?: string | undefined;
                actions?: ({
                    type?: string | undefined;
                } | undefined)[] | undefined;
                conditions?: ({
                    pageUrl?: {
                        hostContains?: string | undefined;
                        hostEquals?: string | undefined;
                        hostPrefix?: string | undefined;
                        hostSuffix?: string | undefined;
                        pathContains?: string | undefined;
                        pathEquals?: string | undefined;
                        pathPrefix?: string | undefined;
                        pathSuffix?: string | undefined;
                        queryContains?: string | undefined;
                        queryEquals?: string | undefined;
                        queryPrefix?: string | undefined;
                        querySuffix?: string | undefined;
                        urlContains?: string | undefined;
                        urlEquals?: string | undefined;
                        urlMatches?: string | undefined;
                        originAndPathMatches?: string | undefined;
                        urlPrefix?: string | undefined;
                        urlSuffix?: string | undefined;
                        schemes?: (string | undefined)[] | undefined;
                        ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                    } | undefined;
                    css?: (string | undefined)[] | undefined;
                    isBookmarked?: boolean | undefined;
                } | undefined)[] | undefined;
            } | undefined)[] | undefined;
            externally_connectable?: {
                ids?: (string | undefined)[] | undefined;
                matches?: (string | undefined)[] | undefined;
                accepts_tls_channel_id?: boolean | undefined;
            } | undefined;
            file_browser_handlers?: ({
                id?: string | undefined;
                default_title?: string | undefined;
                file_filters?: (string | undefined)[] | undefined;
            } | undefined)[] | undefined;
            file_system_provider_capabilities?: {
                configurable?: boolean | undefined;
                watchable?: boolean | undefined;
                multiple_mounts?: boolean | undefined;
                source?: string | undefined;
            } | undefined;
            homepage_url?: string | undefined;
            import?: ({
                id?: string | undefined;
                minimum_version?: string | undefined;
            } | undefined)[] | undefined;
            export?: {
                whitelist?: (string | undefined)[] | undefined;
            } | undefined;
            incognito?: string | undefined;
            input_components?: ({
                name?: string | undefined;
                type?: string | undefined;
                id?: string | undefined;
                description?: string | undefined;
                language?: string | (string | undefined)[] | undefined;
                layouts?: (string | undefined)[] | undefined;
                indicator?: string | undefined;
            } | undefined)[] | undefined;
            key?: string | undefined;
            minimum_chrome_version?: string | undefined;
            nacl_modules?: ({
                path?: string | undefined;
                mime_type?: string | undefined;
            } | undefined)[] | undefined;
            oauth2?: {
                client_id?: string | undefined;
                scopes?: (string | undefined)[] | undefined;
            } | undefined;
            offline_enabled?: boolean | undefined;
            omnibox?: {
                keyword?: string | undefined;
            } | undefined;
            platforms?: ({
                nacl_arch?: string | undefined;
                sub_package_path?: string | undefined;
            } | undefined)[] | undefined;
            plugins?: ({
                path?: string | undefined;
            } | undefined)[] | undefined;
            requirements?: {
                "3D"?: {
                    features?: (string | undefined)[] | undefined;
                } | undefined;
                plugins?: {
                    npapi?: boolean | undefined;
                } | undefined;
            } | undefined;
            short_name?: string | undefined;
            spellcheck?: {
                dictionary_language?: string | undefined;
                dictionary_locale?: string | undefined;
                dictionary_format?: string | undefined;
                dictionary_path?: string | undefined;
            } | undefined;
            storage?: {
                managed_schema?: string | undefined;
            } | undefined;
            tts_engine?: {
                voices?: ({
                    voice_name?: string | undefined;
                    lang?: string | undefined;
                    gender?: string | undefined;
                    event_types?: (string | undefined)[] | undefined;
                } | undefined)[] | undefined;
            } | undefined;
            update_url?: string | undefined;
            version_name?: string | undefined;
            action?: {
                default_icon?: {
                    [x: number]: string | undefined;
                } | undefined;
                default_title?: string | undefined;
                default_popup?: string | undefined;
                browser_style?: boolean | undefined;
            } | undefined;
            browser_action?: {
                default_icon?: {
                    [x: number]: string | undefined;
                } | undefined;
                default_title?: string | undefined;
                default_popup?: string | undefined;
                browser_style?: boolean | undefined;
            } | undefined;
            page_action?: {
                default_icon?: {
                    [x: number]: string | undefined;
                } | undefined;
                default_title?: string | undefined;
                default_popup?: string | undefined;
                browser_style?: boolean | undefined;
            } | undefined;
            browser_specific_settings?: {
                gecko?: {
                    id?: string | undefined;
                    strict_min_version?: string | undefined;
                    strict_max_version?: string | undefined;
                    update_url?: string | undefined;
                } | undefined;
                gecko_android?: {
                    strict_min_version?: string | undefined;
                    strict_max_version?: string | undefined;
                } | undefined;
                safari?: {
                    strict_min_version?: string | undefined;
                    strict_max_version?: string | undefined;
                } | undefined;
            } | undefined;
            permissions?: (chrome.runtime.ManifestPermissions | {
                readonly [x: number]: string | undefined;
                toString?: {} | undefined;
                charAt?: {} | undefined;
                charCodeAt?: {} | undefined;
                concat?: {} | undefined;
                indexOf?: {} | undefined;
                lastIndexOf?: {} | undefined;
                localeCompare?: {} | undefined;
                match?: {} | undefined;
                replace?: {} | undefined;
                search?: {} | undefined;
                slice?: {} | undefined;
                split?: {} | undefined;
                substring?: {} | undefined;
                toLowerCase?: {} | undefined;
                toLocaleLowerCase?: {} | undefined;
                toUpperCase?: {} | undefined;
                toLocaleUpperCase?: {} | undefined;
                trim?: {} | undefined;
                readonly length?: number | undefined;
                substr?: {} | undefined;
                valueOf?: {} | undefined;
                codePointAt?: {} | undefined;
                includes?: {} | undefined;
                endsWith?: {} | undefined;
                normalize?: {} | undefined;
                repeat?: {} | undefined;
                startsWith?: {} | undefined;
                anchor?: {} | undefined;
                big?: {} | undefined;
                blink?: {} | undefined;
                bold?: {} | undefined;
                fixed?: {} | undefined;
                fontcolor?: {} | undefined;
                fontsize?: {} | undefined;
                italics?: {} | undefined;
                link?: {} | undefined;
                small?: {} | undefined;
                strike?: {} | undefined;
                sub?: {} | undefined;
                sup?: {} | undefined;
                padStart?: {} | undefined;
                padEnd?: {} | undefined;
                trimEnd?: {} | undefined;
                trimStart?: {} | undefined;
                trimLeft?: {} | undefined;
                trimRight?: {} | undefined;
                matchAll?: {} | undefined;
                [Symbol.iterator]?: {} | undefined;
                at?: {} | undefined;
            } | undefined)[] | undefined;
            web_accessible_resources?: (string | undefined)[] | ({
                resources?: (string | undefined)[] | undefined;
                matches?: (string | undefined)[] | undefined;
            } | undefined)[] | undefined;
        } | undefined;
        fsCache?: {
            set?: {} | undefined;
            get?: {} | undefined;
        } | undefined;
        runnerConfig?: {
            config?: {
                disabled?: boolean | undefined;
                openConsole?: boolean | undefined;
                openDevtools?: boolean | undefined;
                binaries?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxProfile?: string | undefined;
                chromiumProfile?: string | undefined;
                chromiumPref?: {
                    [x: string]: any;
                } | undefined;
                chromiumPort?: number | undefined;
                firefoxPrefs?: {
                    [x: string]: string | undefined;
                } | undefined;
                firefoxArgs?: (string | undefined)[] | undefined;
                chromiumArgs?: (string | undefined)[] | undefined;
                startUrls?: (string | undefined)[] | undefined;
                keepProfileChanges?: boolean | undefined;
            } | undefined;
            layers?: ({
                config?: {
                    disabled?: boolean | undefined;
                    openConsole?: boolean | undefined;
                    openDevtools?: boolean | undefined;
                    binaries?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxProfile?: string | undefined;
                    chromiumProfile?: string | undefined;
                    chromiumPref?: {
                        [x: string]: any;
                    } | undefined;
                    chromiumPort?: number | undefined;
                    firefoxPrefs?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxArgs?: (string | undefined)[] | undefined;
                    chromiumArgs?: (string | undefined)[] | undefined;
                    startUrls?: (string | undefined)[] | undefined;
                    keepProfileChanges?: boolean | undefined;
                } | null | undefined;
                source?: string | undefined;
                sourceOptions?: {
                    [x: string]: any;
                    meta?: {
                        [x: string]: any;
                        name?: string | undefined;
                    } | undefined;
                    overrides?: {
                        disabled?: boolean | undefined;
                        openConsole?: boolean | undefined;
                        openDevtools?: boolean | undefined;
                        binaries?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxProfile?: string | undefined;
                        chromiumProfile?: string | undefined;
                        chromiumPref?: {
                            [x: string]: any;
                        } | undefined;
                        chromiumPort?: number | undefined;
                        firefoxPrefs?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxArgs?: (string | undefined)[] | undefined;
                        chromiumArgs?: (string | undefined)[] | undefined;
                        startUrls?: (string | undefined)[] | undefined;
                        keepProfileChanges?: boolean | undefined;
                    } | undefined;
                    giget?: {
                        provider?: string | undefined;
                        force?: boolean | undefined;
                        forceClean?: boolean | undefined;
                        offline?: boolean | undefined;
                        preferOffline?: boolean | undefined;
                        providers?: {
                            [x: string]: {} | undefined;
                        } | undefined;
                        dir?: string | undefined;
                        registry?: (false | string) | undefined;
                        cwd?: string | undefined;
                        auth?: string | undefined;
                        install?: boolean | undefined;
                        silent?: boolean | undefined;
                    } | undefined;
                    install?: boolean | undefined;
                    auth?: string | undefined;
                } | undefined;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                cwd?: string | undefined;
                configFile?: string | undefined;
            } | undefined)[] | undefined;
            cwd?: string | undefined;
            source?: string | undefined;
            sourceOptions?: {
                [x: string]: any;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                overrides?: {
                    disabled?: boolean | undefined;
                    openConsole?: boolean | undefined;
                    openDevtools?: boolean | undefined;
                    binaries?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxProfile?: string | undefined;
                    chromiumProfile?: string | undefined;
                    chromiumPref?: {
                        [x: string]: any;
                    } | undefined;
                    chromiumPort?: number | undefined;
                    firefoxPrefs?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    firefoxArgs?: (string | undefined)[] | undefined;
                    chromiumArgs?: (string | undefined)[] | undefined;
                    startUrls?: (string | undefined)[] | undefined;
                    keepProfileChanges?: boolean | undefined;
                } | undefined;
                giget?: {
                    provider?: string | undefined;
                    force?: boolean | undefined;
                    forceClean?: boolean | undefined;
                    offline?: boolean | undefined;
                    preferOffline?: boolean | undefined;
                    providers?: {
                        [x: string]: {} | undefined;
                    } | undefined;
                    dir?: string | undefined;
                    registry?: (false | string) | undefined;
                    cwd?: string | undefined;
                    auth?: string | undefined;
                    install?: boolean | undefined;
                    silent?: boolean | undefined;
                } | undefined;
                install?: boolean | undefined;
                auth?: string | undefined;
            } | undefined;
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            configFile?: string | undefined;
        } | undefined;
        zip?: {
            name?: string | undefined;
            artifactTemplate?: string | undefined;
            sourcesTemplate?: string | undefined;
            includeSources?: (string | undefined)[] | undefined;
            excludeSources?: (string | undefined)[] | undefined;
            sourcesRoot?: string | undefined;
            downloadedPackagesDir?: string | undefined;
            downloadPackages?: (string | undefined)[] | undefined;
            compressionLevel?: 0 | 1 | 9 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | undefined;
            exclude?: (string | undefined)[] | undefined;
            zipSources?: boolean | undefined;
        } | undefined;
        transformManifest?: {} | undefined;
        analysis?: {
            enabled?: boolean | undefined;
            open?: boolean | undefined;
            template?: NonNullable<import("@aklinker1/rollup-plugin-visualizer/dist/plugin/template-types").TemplateType | undefined> | undefined;
            outputFile?: string | undefined;
            outputDir?: string | undefined;
            outputName?: string | undefined;
            keepArtifacts?: boolean | undefined;
        } | undefined;
        userConfigMetadata?: {
            meta?: {
                [x: string]: any;
                name?: string | undefined;
            } | undefined;
            source?: string | undefined;
            configFile?: string | undefined;
            layers?: ({
                config?: {
                    manifest?: {
                        [x: string]: any;
                        content_scripts?: ({
                            matches?: (string | undefined)[] | undefined;
                            exclude_matches?: (string | undefined)[] | undefined;
                            css?: (string | undefined)[] | undefined;
                            js?: (string | undefined)[] | undefined;
                            run_at?: string | undefined;
                            all_frames?: boolean | undefined;
                            match_about_blank?: boolean | undefined;
                            include_globs?: (string | undefined)[] | undefined;
                            exclude_globs?: (string | undefined)[] | undefined;
                            world?: "ISOLATED" | "MAIN" | undefined;
                        } | undefined)[] | undefined;
                        content_security_policy?: {
                            extension_pages?: string | undefined;
                            sandbox?: string | undefined;
                        } | undefined;
                        host_permissions?: (string | undefined)[] | undefined;
                        optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                        optional_host_permissions?: (string | undefined)[] | undefined;
                        name?: string | undefined;
                        version?: string | undefined;
                        default_locale?: string | undefined;
                        description?: string | undefined;
                        icons?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        author?: {
                            email?: string | undefined;
                        } | undefined;
                        background_page?: string | undefined;
                        chrome_settings_overrides?: {
                            homepage?: string | undefined;
                            search_provider?: {
                                name?: string | undefined;
                                keyword?: string | undefined;
                                favicon_url?: string | undefined;
                                search_url?: string | undefined;
                                encoding?: string | undefined;
                                suggest_url?: string | undefined;
                                instant_url?: string | undefined;
                                image_url?: string | undefined;
                                search_url_post_params?: string | undefined;
                                suggest_url_post_params?: string | undefined;
                                instant_url_post_params?: string | undefined;
                                image_url_post_params?: string | undefined;
                                alternate_urls?: (string | undefined)[] | undefined;
                                prepopulated_id?: number | undefined;
                                is_default?: boolean | undefined;
                            } | undefined;
                            startup_pages?: (string | undefined)[] | undefined;
                        } | undefined;
                        chrome_ui_overrides?: {
                            bookmarks_ui?: {
                                remove_bookmark_shortcut?: boolean | undefined;
                                remove_button?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        commands?: {
                            [x: string]: {
                                suggested_key?: {
                                    default?: string | undefined;
                                    windows?: string | undefined;
                                    mac?: string | undefined;
                                    chromeos?: string | undefined;
                                    linux?: string | undefined;
                                } | undefined;
                                description?: string | undefined;
                                global?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        content_capabilities?: {
                            matches?: (string | undefined)[] | undefined;
                            permissions?: (string | undefined)[] | undefined;
                        } | undefined;
                        converted_from_user_script?: boolean | undefined;
                        current_locale?: string | undefined;
                        event_rules?: ({
                            event?: string | undefined;
                            actions?: ({
                                type?: string | undefined;
                            } | undefined)[] | undefined;
                            conditions?: ({
                                pageUrl?: {
                                    hostContains?: string | undefined;
                                    hostEquals?: string | undefined;
                                    hostPrefix?: string | undefined;
                                    hostSuffix?: string | undefined;
                                    pathContains?: string | undefined;
                                    pathEquals?: string | undefined;
                                    pathPrefix?: string | undefined;
                                    pathSuffix?: string | undefined;
                                    queryContains?: string | undefined;
                                    queryEquals?: string | undefined;
                                    queryPrefix?: string | undefined;
                                    querySuffix?: string | undefined;
                                    urlContains?: string | undefined;
                                    urlEquals?: string | undefined;
                                    urlMatches?: string | undefined;
                                    originAndPathMatches?: string | undefined;
                                    urlPrefix?: string | undefined;
                                    urlSuffix?: string | undefined;
                                    schemes?: (string | undefined)[] | undefined;
                                    ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                                } | undefined;
                                css?: (string | undefined)[] | undefined;
                                isBookmarked?: boolean | undefined;
                            } | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        externally_connectable?: {
                            ids?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                            accepts_tls_channel_id?: boolean | undefined;
                        } | undefined;
                        file_browser_handlers?: ({
                            id?: string | undefined;
                            default_title?: string | undefined;
                            file_filters?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        file_system_provider_capabilities?: {
                            configurable?: boolean | undefined;
                            watchable?: boolean | undefined;
                            multiple_mounts?: boolean | undefined;
                            source?: string | undefined;
                        } | undefined;
                        homepage_url?: string | undefined;
                        import?: ({
                            id?: string | undefined;
                            minimum_version?: string | undefined;
                        } | undefined)[] | undefined;
                        export?: {
                            whitelist?: (string | undefined)[] | undefined;
                        } | undefined;
                        incognito?: string | undefined;
                        input_components?: ({
                            name?: string | undefined;
                            type?: string | undefined;
                            id?: string | undefined;
                            description?: string | undefined;
                            language?: string | (string | undefined)[] | undefined;
                            layouts?: (string | undefined)[] | undefined;
                            indicator?: string | undefined;
                        } | undefined)[] | undefined;
                        key?: string | undefined;
                        minimum_chrome_version?: string | undefined;
                        nacl_modules?: ({
                            path?: string | undefined;
                            mime_type?: string | undefined;
                        } | undefined)[] | undefined;
                        oauth2?: {
                            client_id?: string | undefined;
                            scopes?: (string | undefined)[] | undefined;
                        } | undefined;
                        offline_enabled?: boolean | undefined;
                        omnibox?: {
                            keyword?: string | undefined;
                        } | undefined;
                        platforms?: ({
                            nacl_arch?: string | undefined;
                            sub_package_path?: string | undefined;
                        } | undefined)[] | undefined;
                        plugins?: ({
                            path?: string | undefined;
                        } | undefined)[] | undefined;
                        requirements?: {
                            "3D"?: {
                                features?: (string | undefined)[] | undefined;
                            } | undefined;
                            plugins?: {
                                npapi?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        short_name?: string | undefined;
                        spellcheck?: {
                            dictionary_language?: string | undefined;
                            dictionary_locale?: string | undefined;
                            dictionary_format?: string | undefined;
                            dictionary_path?: string | undefined;
                        } | undefined;
                        storage?: {
                            managed_schema?: string | undefined;
                        } | undefined;
                        tts_engine?: {
                            voices?: ({
                                voice_name?: string | undefined;
                                lang?: string | undefined;
                                gender?: string | undefined;
                                event_types?: (string | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                        } | undefined;
                        update_url?: string | undefined;
                        version_name?: string | undefined;
                        action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        page_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_specific_settings?: {
                            gecko?: {
                                id?: string | undefined;
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                                update_url?: string | undefined;
                            } | undefined;
                            gecko_android?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                            safari?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                        } | undefined;
                        permissions?: (chrome.runtime.ManifestPermissions | {
                            readonly [x: number]: string | undefined;
                            toString?: {} | undefined;
                            charAt?: {} | undefined;
                            charCodeAt?: {} | undefined;
                            concat?: {} | undefined;
                            indexOf?: {} | undefined;
                            lastIndexOf?: {} | undefined;
                            localeCompare?: {} | undefined;
                            match?: {} | undefined;
                            replace?: {} | undefined;
                            search?: {} | undefined;
                            slice?: {} | undefined;
                            split?: {} | undefined;
                            substring?: {} | undefined;
                            toLowerCase?: {} | undefined;
                            toLocaleLowerCase?: {} | undefined;
                            toUpperCase?: {} | undefined;
                            toLocaleUpperCase?: {} | undefined;
                            trim?: {} | undefined;
                            readonly length?: number | undefined;
                            substr?: {} | undefined;
                            valueOf?: {} | undefined;
                            codePointAt?: {} | undefined;
                            includes?: {} | undefined;
                            endsWith?: {} | undefined;
                            normalize?: {} | undefined;
                            repeat?: {} | undefined;
                            startsWith?: {} | undefined;
                            anchor?: {} | undefined;
                            big?: {} | undefined;
                            blink?: {} | undefined;
                            bold?: {} | undefined;
                            fixed?: {} | undefined;
                            fontcolor?: {} | undefined;
                            fontsize?: {} | undefined;
                            italics?: {} | undefined;
                            link?: {} | undefined;
                            small?: {} | undefined;
                            strike?: {} | undefined;
                            sub?: {} | undefined;
                            sup?: {} | undefined;
                            padStart?: {} | undefined;
                            padEnd?: {} | undefined;
                            trimEnd?: {} | undefined;
                            trimStart?: {} | undefined;
                            trimLeft?: {} | undefined;
                            trimRight?: {} | undefined;
                            matchAll?: {} | undefined;
                            [Symbol.iterator]?: {} | undefined;
                            at?: {} | undefined;
                        } | undefined)[] | undefined;
                        web_accessible_resources?: (string | undefined)[] | ({
                            resources?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                    } | {
                        then?: {} | undefined;
                        catch?: {} | undefined;
                        finally?: {} | undefined;
                        readonly [Symbol.toStringTag]?: string | undefined;
                    } | {} | undefined;
                    vite?: {} | undefined;
                    zip?: {
                        artifactTemplate?: string | undefined;
                        zipSources?: boolean | undefined;
                        sourcesTemplate?: string | undefined;
                        name?: string | undefined;
                        sourcesRoot?: string | undefined;
                        includeSources?: (string | undefined)[] | undefined;
                        excludeSources?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        downloadPackages?: (string | undefined)[] | undefined;
                        compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                    } | undefined;
                    root?: string | undefined;
                    mode?: string | undefined;
                    dev?: {
                        server?: {
                            port?: number | undefined;
                            hostname?: string | undefined;
                        } | undefined;
                        reloadCommand?: (string | false) | undefined;
                    } | undefined;
                    publicDir?: string | undefined;
                    experimental?: {} | undefined;
                    srcDir?: string | undefined;
                    entrypointsDir?: string | undefined;
                    modulesDir?: string | undefined;
                    filterEntrypoints?: (string | undefined)[] | undefined;
                    outDir?: string | undefined;
                    outDirTemplate?: string | undefined;
                    debug?: boolean | undefined;
                    imports?: false | {
                        imports?: ({
                            name?: string | undefined;
                            as?: import("unimport").ImportName | undefined;
                            with?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | undefined)[] | undefined;
                        presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                            imports?: (string | any | {
                                meta?: {
                                    [x: string]: any;
                                    description?: string | undefined;
                                    docsUrl?: string | undefined;
                                } | undefined;
                                name?: string | undefined;
                                type?: boolean | undefined;
                                as?: import("unimport").ImportName | undefined;
                                with?: {
                                    [x: string]: string | undefined;
                                } | undefined;
                                priority?: number | undefined;
                                disabled?: boolean | undefined;
                                dtsDisabled?: boolean | undefined;
                                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                typeFrom?: import("unimport").ModuleId | undefined;
                            } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | {
                            package?: string | undefined;
                            url?: string | undefined;
                            ignore?: (string | {
                                exec?: {} | undefined;
                                test?: {} | undefined;
                                readonly source?: string | undefined;
                                readonly global?: boolean | undefined;
                                readonly ignoreCase?: boolean | undefined;
                                readonly multiline?: boolean | undefined;
                                lastIndex?: number | undefined;
                                compile?: {} | undefined;
                                readonly flags?: string | undefined;
                                readonly sticky?: boolean | undefined;
                                readonly unicode?: boolean | undefined;
                                readonly dotAll?: boolean | undefined;
                                [Symbol.match]?: {} | undefined;
                                [Symbol.replace]?: {} | undefined;
                                [Symbol.search]?: {} | undefined;
                                [Symbol.split]?: {} | undefined;
                                [Symbol.matchAll]?: {} | undefined;
                            } | {} | undefined)[] | undefined;
                            cache?: boolean | undefined;
                        } | undefined)[] | undefined;
                        warn?: {} | undefined;
                        debugLog?: {} | undefined;
                        addons?: {
                            addons?: ({
                                name?: string | undefined;
                                transform?: {} | undefined;
                                declaration?: {} | undefined;
                                matchImports?: {} | undefined;
                                extendImports?: {} | undefined;
                                injectImportsResolved?: {} | undefined;
                                injectImportsStringified?: {} | undefined;
                            } | undefined)[] | undefined;
                            vueTemplate?: boolean | undefined;
                            vueDirectives?: true | {
                                isDirective?: {} | undefined;
                            } | undefined;
                        } | ({
                            name?: string | undefined;
                            transform?: {} | undefined;
                            declaration?: {} | undefined;
                            matchImports?: {} | undefined;
                            extendImports?: {} | undefined;
                            injectImportsResolved?: {} | undefined;
                            injectImportsStringified?: {} | undefined;
                        } | undefined)[] | undefined;
                        virtualImports?: (string | undefined)[] | undefined;
                        dirs?: (string | undefined)[] | undefined;
                        dirsScanOptions?: {
                            filePatterns?: (string | undefined)[] | undefined;
                            fileFilter?: {} | undefined;
                            types?: boolean | undefined;
                            cwd?: string | undefined;
                        } | undefined;
                        resolveId?: {} | undefined;
                        commentsDisable?: (string | undefined)[] | undefined;
                        commentsDebug?: (string | undefined)[] | undefined;
                        collectMeta?: boolean | undefined;
                        injectAtEnd?: boolean | undefined;
                        mergeExisting?: boolean | undefined;
                        parser?: ("acorn" | "regex") | undefined;
                        eslintrc?: {
                            enabled?: (false | true | "auto" | 8 | 9) | undefined;
                            filePath?: string | undefined;
                            globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                        } | undefined;
                    } | undefined;
                    browser?: import("../../../types").TargetBrowser | undefined;
                    manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                    logger?: {
                        debug?: {} | undefined;
                        log?: {} | undefined;
                        info?: {} | undefined;
                        warn?: {} | undefined;
                        error?: {} | undefined;
                        fatal?: {} | undefined;
                        success?: {} | undefined;
                        level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                            toString?: {} | undefined;
                            toFixed?: {} | undefined;
                            toExponential?: {} | undefined;
                            toPrecision?: {} | undefined;
                            valueOf?: {} | undefined;
                            toLocaleString?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    runner?: {
                        disabled?: boolean | undefined;
                        openConsole?: boolean | undefined;
                        openDevtools?: boolean | undefined;
                        binaries?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxProfile?: string | undefined;
                        chromiumProfile?: string | undefined;
                        chromiumPref?: {
                            [x: string]: any;
                        } | undefined;
                        chromiumPort?: number | undefined;
                        firefoxPrefs?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxArgs?: (string | undefined)[] | undefined;
                        chromiumArgs?: (string | undefined)[] | undefined;
                        startUrls?: (string | undefined)[] | undefined;
                        keepProfileChanges?: boolean | undefined;
                    } | undefined;
                    transformManifest?: {} | undefined;
                    analysis?: {
                        enabled?: boolean | undefined;
                        open?: boolean | undefined;
                        template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                        outputFile?: string | undefined;
                        keepArtifacts?: boolean | undefined;
                    } | undefined;
                    alias?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                    entrypointLoader?: ("vite-node" | "jiti") | undefined;
                    hooks?: {
                        'vite:build:extendConfig'?: {} | undefined;
                        'vite:devServer:extendConfig'?: {} | undefined;
                        ready?: {} | undefined;
                        'config:resolved'?: {} | undefined;
                        'prepare:types'?: {} | undefined;
                        'prepare:publicPaths'?: {} | undefined;
                        'build:before'?: {} | undefined;
                        'build:done'?: {} | undefined;
                        'build:manifestGenerated'?: {} | undefined;
                        'entrypoints:found'?: {} | undefined;
                        'entrypoints:resolved'?: {} | undefined;
                        'entrypoints:grouped'?: {} | undefined;
                        'build:publicAssets'?: {} | undefined;
                        'zip:start'?: {} | undefined;
                        'zip:extension:start'?: {} | undefined;
                        'zip:extension:done'?: {} | undefined;
                        'zip:sources:start'?: {} | undefined;
                        'zip:sources:done'?: {} | undefined;
                        'zip:done'?: {} | undefined;
                        'server:created'?: {} | undefined;
                        'server:started'?: {} | undefined;
                        'server:closed'?: {} | undefined;
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                    } | {
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                        ready?: {} | undefined;
                    } | undefined;
                    modules?: (string | undefined)[] | undefined;
                } | null | undefined;
                source?: string | undefined;
                sourceOptions?: {
                    [x: string]: any;
                    meta?: {
                        [x: string]: any;
                        name?: string | undefined;
                    } | undefined;
                    overrides?: {
                        manifest?: {
                            [x: string]: any;
                            content_scripts?: ({
                                matches?: (string | undefined)[] | undefined;
                                exclude_matches?: (string | undefined)[] | undefined;
                                css?: (string | undefined)[] | undefined;
                                js?: (string | undefined)[] | undefined;
                                run_at?: string | undefined;
                                all_frames?: boolean | undefined;
                                match_about_blank?: boolean | undefined;
                                include_globs?: (string | undefined)[] | undefined;
                                exclude_globs?: (string | undefined)[] | undefined;
                                world?: "ISOLATED" | "MAIN" | undefined;
                            } | undefined)[] | undefined;
                            content_security_policy?: {
                                extension_pages?: string | undefined;
                                sandbox?: string | undefined;
                            } | undefined;
                            host_permissions?: (string | undefined)[] | undefined;
                            optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                            optional_host_permissions?: (string | undefined)[] | undefined;
                            name?: string | undefined;
                            version?: string | undefined;
                            default_locale?: string | undefined;
                            description?: string | undefined;
                            icons?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            author?: {
                                email?: string | undefined;
                            } | undefined;
                            background_page?: string | undefined;
                            chrome_settings_overrides?: {
                                homepage?: string | undefined;
                                search_provider?: {
                                    name?: string | undefined;
                                    keyword?: string | undefined;
                                    favicon_url?: string | undefined;
                                    search_url?: string | undefined;
                                    encoding?: string | undefined;
                                    suggest_url?: string | undefined;
                                    instant_url?: string | undefined;
                                    image_url?: string | undefined;
                                    search_url_post_params?: string | undefined;
                                    suggest_url_post_params?: string | undefined;
                                    instant_url_post_params?: string | undefined;
                                    image_url_post_params?: string | undefined;
                                    alternate_urls?: (string | undefined)[] | undefined;
                                    prepopulated_id?: number | undefined;
                                    is_default?: boolean | undefined;
                                } | undefined;
                                startup_pages?: (string | undefined)[] | undefined;
                            } | undefined;
                            chrome_ui_overrides?: {
                                bookmarks_ui?: {
                                    remove_bookmark_shortcut?: boolean | undefined;
                                    remove_button?: boolean | undefined;
                                } | undefined;
                            } | undefined;
                            commands?: {
                                [x: string]: {
                                    suggested_key?: {
                                        default?: string | undefined;
                                        windows?: string | undefined;
                                        mac?: string | undefined;
                                        chromeos?: string | undefined;
                                        linux?: string | undefined;
                                    } | undefined;
                                    description?: string | undefined;
                                    global?: boolean | undefined;
                                } | undefined;
                            } | undefined;
                            content_capabilities?: {
                                matches?: (string | undefined)[] | undefined;
                                permissions?: (string | undefined)[] | undefined;
                            } | undefined;
                            converted_from_user_script?: boolean | undefined;
                            current_locale?: string | undefined;
                            event_rules?: ({
                                event?: string | undefined;
                                actions?: ({
                                    type?: string | undefined;
                                } | undefined)[] | undefined;
                                conditions?: ({
                                    pageUrl?: {
                                        hostContains?: string | undefined;
                                        hostEquals?: string | undefined;
                                        hostPrefix?: string | undefined;
                                        hostSuffix?: string | undefined;
                                        pathContains?: string | undefined;
                                        pathEquals?: string | undefined;
                                        pathPrefix?: string | undefined;
                                        pathSuffix?: string | undefined;
                                        queryContains?: string | undefined;
                                        queryEquals?: string | undefined;
                                        queryPrefix?: string | undefined;
                                        querySuffix?: string | undefined;
                                        urlContains?: string | undefined;
                                        urlEquals?: string | undefined;
                                        urlMatches?: string | undefined;
                                        originAndPathMatches?: string | undefined;
                                        urlPrefix?: string | undefined;
                                        urlSuffix?: string | undefined;
                                        schemes?: (string | undefined)[] | undefined;
                                        ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                                    } | undefined;
                                    css?: (string | undefined)[] | undefined;
                                    isBookmarked?: boolean | undefined;
                                } | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                            externally_connectable?: {
                                ids?: (string | undefined)[] | undefined;
                                matches?: (string | undefined)[] | undefined;
                                accepts_tls_channel_id?: boolean | undefined;
                            } | undefined;
                            file_browser_handlers?: ({
                                id?: string | undefined;
                                default_title?: string | undefined;
                                file_filters?: (string | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                            file_system_provider_capabilities?: {
                                configurable?: boolean | undefined;
                                watchable?: boolean | undefined;
                                multiple_mounts?: boolean | undefined;
                                source?: string | undefined;
                            } | undefined;
                            homepage_url?: string | undefined;
                            import?: ({
                                id?: string | undefined;
                                minimum_version?: string | undefined;
                            } | undefined)[] | undefined;
                            export?: {
                                whitelist?: (string | undefined)[] | undefined;
                            } | undefined;
                            incognito?: string | undefined;
                            input_components?: ({
                                name?: string | undefined;
                                type?: string | undefined;
                                id?: string | undefined;
                                description?: string | undefined;
                                language?: string | (string | undefined)[] | undefined;
                                layouts?: (string | undefined)[] | undefined;
                                indicator?: string | undefined;
                            } | undefined)[] | undefined;
                            key?: string | undefined;
                            minimum_chrome_version?: string | undefined;
                            nacl_modules?: ({
                                path?: string | undefined;
                                mime_type?: string | undefined;
                            } | undefined)[] | undefined;
                            oauth2?: {
                                client_id?: string | undefined;
                                scopes?: (string | undefined)[] | undefined;
                            } | undefined;
                            offline_enabled?: boolean | undefined;
                            omnibox?: {
                                keyword?: string | undefined;
                            } | undefined;
                            platforms?: ({
                                nacl_arch?: string | undefined;
                                sub_package_path?: string | undefined;
                            } | undefined)[] | undefined;
                            plugins?: ({
                                path?: string | undefined;
                            } | undefined)[] | undefined;
                            requirements?: {
                                "3D"?: {
                                    features?: (string | undefined)[] | undefined;
                                } | undefined;
                                plugins?: {
                                    npapi?: boolean | undefined;
                                } | undefined;
                            } | undefined;
                            short_name?: string | undefined;
                            spellcheck?: {
                                dictionary_language?: string | undefined;
                                dictionary_locale?: string | undefined;
                                dictionary_format?: string | undefined;
                                dictionary_path?: string | undefined;
                            } | undefined;
                            storage?: {
                                managed_schema?: string | undefined;
                            } | undefined;
                            tts_engine?: {
                                voices?: ({
                                    voice_name?: string | undefined;
                                    lang?: string | undefined;
                                    gender?: string | undefined;
                                    event_types?: (string | undefined)[] | undefined;
                                } | undefined)[] | undefined;
                            } | undefined;
                            update_url?: string | undefined;
                            version_name?: string | undefined;
                            action?: {
                                default_icon?: {
                                    [x: number]: string | undefined;
                                } | undefined;
                                default_title?: string | undefined;
                                default_popup?: string | undefined;
                                browser_style?: boolean | undefined;
                            } | undefined;
                            browser_action?: {
                                default_icon?: {
                                    [x: number]: string | undefined;
                                } | undefined;
                                default_title?: string | undefined;
                                default_popup?: string | undefined;
                                browser_style?: boolean | undefined;
                            } | undefined;
                            page_action?: {
                                default_icon?: {
                                    [x: number]: string | undefined;
                                } | undefined;
                                default_title?: string | undefined;
                                default_popup?: string | undefined;
                                browser_style?: boolean | undefined;
                            } | undefined;
                            browser_specific_settings?: {
                                gecko?: {
                                    id?: string | undefined;
                                    strict_min_version?: string | undefined;
                                    strict_max_version?: string | undefined;
                                    update_url?: string | undefined;
                                } | undefined;
                                gecko_android?: {
                                    strict_min_version?: string | undefined;
                                    strict_max_version?: string | undefined;
                                } | undefined;
                                safari?: {
                                    strict_min_version?: string | undefined;
                                    strict_max_version?: string | undefined;
                                } | undefined;
                            } | undefined;
                            permissions?: (chrome.runtime.ManifestPermissions | {
                                readonly [x: number]: string | undefined;
                                toString?: {} | undefined;
                                charAt?: {} | undefined;
                                charCodeAt?: {} | undefined;
                                concat?: {} | undefined;
                                indexOf?: {} | undefined;
                                lastIndexOf?: {} | undefined;
                                localeCompare?: {} | undefined;
                                match?: {} | undefined;
                                replace?: {} | undefined;
                                search?: {} | undefined;
                                slice?: {} | undefined;
                                split?: {} | undefined;
                                substring?: {} | undefined;
                                toLowerCase?: {} | undefined;
                                toLocaleLowerCase?: {} | undefined;
                                toUpperCase?: {} | undefined;
                                toLocaleUpperCase?: {} | undefined;
                                trim?: {} | undefined;
                                readonly length?: number | undefined;
                                substr?: {} | undefined;
                                valueOf?: {} | undefined;
                                codePointAt?: {} | undefined;
                                includes?: {} | undefined;
                                endsWith?: {} | undefined;
                                normalize?: {} | undefined;
                                repeat?: {} | undefined;
                                startsWith?: {} | undefined;
                                anchor?: {} | undefined;
                                big?: {} | undefined;
                                blink?: {} | undefined;
                                bold?: {} | undefined;
                                fixed?: {} | undefined;
                                fontcolor?: {} | undefined;
                                fontsize?: {} | undefined;
                                italics?: {} | undefined;
                                link?: {} | undefined;
                                small?: {} | undefined;
                                strike?: {} | undefined;
                                sub?: {} | undefined;
                                sup?: {} | undefined;
                                padStart?: {} | undefined;
                                padEnd?: {} | undefined;
                                trimEnd?: {} | undefined;
                                trimStart?: {} | undefined;
                                trimLeft?: {} | undefined;
                                trimRight?: {} | undefined;
                                matchAll?: {} | undefined;
                                [Symbol.iterator]?: {} | undefined;
                                at?: {} | undefined;
                            } | undefined)[] | undefined;
                            web_accessible_resources?: (string | undefined)[] | ({
                                resources?: (string | undefined)[] | undefined;
                                matches?: (string | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                        } | {
                            then?: {} | undefined;
                            catch?: {} | undefined;
                            finally?: {} | undefined;
                            readonly [Symbol.toStringTag]?: string | undefined;
                        } | {} | undefined;
                        vite?: {} | undefined;
                        zip?: {
                            artifactTemplate?: string | undefined;
                            zipSources?: boolean | undefined;
                            sourcesTemplate?: string | undefined;
                            name?: string | undefined;
                            sourcesRoot?: string | undefined;
                            includeSources?: (string | undefined)[] | undefined;
                            excludeSources?: (string | undefined)[] | undefined;
                            exclude?: (string | undefined)[] | undefined;
                            downloadPackages?: (string | undefined)[] | undefined;
                            compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                        } | undefined;
                        root?: string | undefined;
                        mode?: string | undefined;
                        dev?: {
                            server?: {
                                port?: number | undefined;
                                hostname?: string | undefined;
                            } | undefined;
                            reloadCommand?: (string | false) | undefined;
                        } | undefined;
                        publicDir?: string | undefined;
                        experimental?: {} | undefined;
                        srcDir?: string | undefined;
                        entrypointsDir?: string | undefined;
                        modulesDir?: string | undefined;
                        filterEntrypoints?: (string | undefined)[] | undefined;
                        outDir?: string | undefined;
                        outDirTemplate?: string | undefined;
                        debug?: boolean | undefined;
                        imports?: false | {
                            imports?: ({
                                name?: string | undefined;
                                as?: import("unimport").ImportName | undefined;
                                with?: {
                                    [x: string]: string | undefined;
                                } | undefined;
                                from?: string | undefined;
                                priority?: number | undefined;
                                disabled?: boolean | undefined;
                                dtsDisabled?: boolean | undefined;
                                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                meta?: {
                                    [x: string]: any;
                                    description?: string | undefined;
                                    docsUrl?: string | undefined;
                                } | undefined;
                                type?: boolean | undefined;
                                typeFrom?: import("unimport").ModuleId | undefined;
                            } | undefined)[] | undefined;
                            presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                                imports?: (string | any | {
                                    meta?: {
                                        [x: string]: any;
                                        description?: string | undefined;
                                        docsUrl?: string | undefined;
                                    } | undefined;
                                    name?: string | undefined;
                                    type?: boolean | undefined;
                                    as?: import("unimport").ImportName | undefined;
                                    with?: {
                                        [x: string]: string | undefined;
                                    } | undefined;
                                    priority?: number | undefined;
                                    disabled?: boolean | undefined;
                                    dtsDisabled?: boolean | undefined;
                                    declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                    typeFrom?: import("unimport").ModuleId | undefined;
                                } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                                from?: string | undefined;
                                priority?: number | undefined;
                                disabled?: boolean | undefined;
                                dtsDisabled?: boolean | undefined;
                                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                meta?: {
                                    [x: string]: any;
                                    description?: string | undefined;
                                    docsUrl?: string | undefined;
                                } | undefined;
                                type?: boolean | undefined;
                                typeFrom?: import("unimport").ModuleId | undefined;
                            } | {
                                package?: string | undefined;
                                url?: string | undefined;
                                ignore?: (string | {
                                    exec?: {} | undefined;
                                    test?: {} | undefined;
                                    readonly source?: string | undefined;
                                    readonly global?: boolean | undefined;
                                    readonly ignoreCase?: boolean | undefined;
                                    readonly multiline?: boolean | undefined;
                                    lastIndex?: number | undefined;
                                    compile?: {} | undefined;
                                    readonly flags?: string | undefined;
                                    readonly sticky?: boolean | undefined;
                                    readonly unicode?: boolean | undefined;
                                    readonly dotAll?: boolean | undefined;
                                    [Symbol.match]?: {} | undefined;
                                    [Symbol.replace]?: {} | undefined;
                                    [Symbol.search]?: {} | undefined;
                                    [Symbol.split]?: {} | undefined;
                                    [Symbol.matchAll]?: {} | undefined;
                                } | {} | undefined)[] | undefined;
                                cache?: boolean | undefined;
                            } | undefined)[] | undefined;
                            warn?: {} | undefined;
                            debugLog?: {} | undefined;
                            addons?: {
                                addons?: ({
                                    name?: string | undefined;
                                    transform?: {} | undefined;
                                    declaration?: {} | undefined;
                                    matchImports?: {} | undefined;
                                    extendImports?: {} | undefined;
                                    injectImportsResolved?: {} | undefined;
                                    injectImportsStringified?: {} | undefined;
                                } | undefined)[] | undefined;
                                vueTemplate?: boolean | undefined;
                                vueDirectives?: true | {
                                    isDirective?: {} | undefined;
                                } | undefined;
                            } | ({
                                name?: string | undefined;
                                transform?: {} | undefined;
                                declaration?: {} | undefined;
                                matchImports?: {} | undefined;
                                extendImports?: {} | undefined;
                                injectImportsResolved?: {} | undefined;
                                injectImportsStringified?: {} | undefined;
                            } | undefined)[] | undefined;
                            virtualImports?: (string | undefined)[] | undefined;
                            dirs?: (string | undefined)[] | undefined;
                            dirsScanOptions?: {
                                filePatterns?: (string | undefined)[] | undefined;
                                fileFilter?: {} | undefined;
                                types?: boolean | undefined;
                                cwd?: string | undefined;
                            } | undefined;
                            resolveId?: {} | undefined;
                            commentsDisable?: (string | undefined)[] | undefined;
                            commentsDebug?: (string | undefined)[] | undefined;
                            collectMeta?: boolean | undefined;
                            injectAtEnd?: boolean | undefined;
                            mergeExisting?: boolean | undefined;
                            parser?: ("acorn" | "regex") | undefined;
                            eslintrc?: {
                                enabled?: (false | true | "auto" | 8 | 9) | undefined;
                                filePath?: string | undefined;
                                globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                            } | undefined;
                        } | undefined;
                        browser?: import("../../../types").TargetBrowser | undefined;
                        manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                        logger?: {
                            debug?: {} | undefined;
                            log?: {} | undefined;
                            info?: {} | undefined;
                            warn?: {} | undefined;
                            error?: {} | undefined;
                            fatal?: {} | undefined;
                            success?: {} | undefined;
                            level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                                toString?: {} | undefined;
                                toFixed?: {} | undefined;
                                toExponential?: {} | undefined;
                                toPrecision?: {} | undefined;
                                valueOf?: {} | undefined;
                                toLocaleString?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        runner?: {
                            disabled?: boolean | undefined;
                            openConsole?: boolean | undefined;
                            openDevtools?: boolean | undefined;
                            binaries?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            firefoxProfile?: string | undefined;
                            chromiumProfile?: string | undefined;
                            chromiumPref?: {
                                [x: string]: any;
                            } | undefined;
                            chromiumPort?: number | undefined;
                            firefoxPrefs?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            firefoxArgs?: (string | undefined)[] | undefined;
                            chromiumArgs?: (string | undefined)[] | undefined;
                            startUrls?: (string | undefined)[] | undefined;
                            keepProfileChanges?: boolean | undefined;
                        } | undefined;
                        transformManifest?: {} | undefined;
                        analysis?: {
                            enabled?: boolean | undefined;
                            open?: boolean | undefined;
                            template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                            outputFile?: string | undefined;
                            keepArtifacts?: boolean | undefined;
                        } | undefined;
                        alias?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                        entrypointLoader?: ("vite-node" | "jiti") | undefined;
                        hooks?: {
                            'vite:build:extendConfig'?: {} | undefined;
                            'vite:devServer:extendConfig'?: {} | undefined;
                            ready?: {} | undefined;
                            'config:resolved'?: {} | undefined;
                            'prepare:types'?: {} | undefined;
                            'prepare:publicPaths'?: {} | undefined;
                            'build:before'?: {} | undefined;
                            'build:done'?: {} | undefined;
                            'build:manifestGenerated'?: {} | undefined;
                            'entrypoints:found'?: {} | undefined;
                            'entrypoints:resolved'?: {} | undefined;
                            'entrypoints:grouped'?: {} | undefined;
                            'build:publicAssets'?: {} | undefined;
                            'zip:start'?: {} | undefined;
                            'zip:extension:start'?: {} | undefined;
                            'zip:extension:done'?: {} | undefined;
                            'zip:sources:start'?: {} | undefined;
                            'zip:sources:done'?: {} | undefined;
                            'zip:done'?: {} | undefined;
                            'server:created'?: {} | undefined;
                            'server:started'?: {} | undefined;
                            'server:closed'?: {} | undefined;
                            vite?: {
                                "build:extendConfig"?: {} | undefined;
                                "devServer:extendConfig"?: {} | undefined;
                                build?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                                devServer?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                            } | {
                                build?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                                devServer?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                            } | undefined;
                            config?: {
                                resolved?: {} | undefined;
                            } | {
                                resolved?: {} | undefined;
                            } | undefined;
                            prepare?: {
                                types?: {} | undefined;
                                publicPaths?: {} | undefined;
                            } | {
                                types?: {} | undefined;
                                publicPaths?: {} | undefined;
                            } | undefined;
                            build?: {
                                before?: {} | undefined;
                                done?: {} | undefined;
                                manifestGenerated?: {} | undefined;
                                publicAssets?: {} | undefined;
                            } | {
                                before?: {} | undefined;
                                done?: {} | undefined;
                                manifestGenerated?: {} | undefined;
                                publicAssets?: {} | undefined;
                            } | undefined;
                            entrypoints?: {
                                resolved?: {} | undefined;
                                found?: {} | undefined;
                                grouped?: {} | undefined;
                            } | {
                                resolved?: {} | undefined;
                                found?: {} | undefined;
                                grouped?: {} | undefined;
                            } | undefined;
                            zip?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                                "extension:start"?: {} | undefined;
                                "extension:done"?: {} | undefined;
                                "sources:start"?: {} | undefined;
                                "sources:done"?: {} | undefined;
                                extension?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                sources?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                            } | {
                                extension?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                sources?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            server?: {
                                closed?: {} | undefined;
                                created?: {} | undefined;
                                started?: {} | undefined;
                            } | {
                                closed?: {} | undefined;
                                created?: {} | undefined;
                                started?: {} | undefined;
                            } | undefined;
                        } | {
                            vite?: {
                                "build:extendConfig"?: {} | undefined;
                                "devServer:extendConfig"?: {} | undefined;
                                build?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                                devServer?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                            } | {
                                build?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                                devServer?: {
                                    extendConfig?: {} | undefined;
                                } | {
                                    extendConfig?: {} | undefined;
                                } | undefined;
                            } | undefined;
                            config?: {
                                resolved?: {} | undefined;
                            } | {
                                resolved?: {} | undefined;
                            } | undefined;
                            prepare?: {
                                types?: {} | undefined;
                                publicPaths?: {} | undefined;
                            } | {
                                types?: {} | undefined;
                                publicPaths?: {} | undefined;
                            } | undefined;
                            build?: {
                                before?: {} | undefined;
                                done?: {} | undefined;
                                manifestGenerated?: {} | undefined;
                                publicAssets?: {} | undefined;
                            } | {
                                before?: {} | undefined;
                                done?: {} | undefined;
                                manifestGenerated?: {} | undefined;
                                publicAssets?: {} | undefined;
                            } | undefined;
                            entrypoints?: {
                                resolved?: {} | undefined;
                                found?: {} | undefined;
                                grouped?: {} | undefined;
                            } | {
                                resolved?: {} | undefined;
                                found?: {} | undefined;
                                grouped?: {} | undefined;
                            } | undefined;
                            zip?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                                "extension:start"?: {} | undefined;
                                "extension:done"?: {} | undefined;
                                "sources:start"?: {} | undefined;
                                "sources:done"?: {} | undefined;
                                extension?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                sources?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                            } | {
                                extension?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                sources?: {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | {
                                    done?: {} | undefined;
                                    start?: {} | undefined;
                                } | undefined;
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            server?: {
                                closed?: {} | undefined;
                                created?: {} | undefined;
                                started?: {} | undefined;
                            } | {
                                closed?: {} | undefined;
                                created?: {} | undefined;
                                started?: {} | undefined;
                            } | undefined;
                            ready?: {} | undefined;
                        } | undefined;
                        modules?: (string | undefined)[] | undefined;
                    } | undefined;
                    giget?: {
                        provider?: string | undefined;
                        force?: boolean | undefined;
                        forceClean?: boolean | undefined;
                        offline?: boolean | undefined;
                        preferOffline?: boolean | undefined;
                        providers?: {
                            [x: string]: {} | undefined;
                        } | undefined;
                        dir?: string | undefined;
                        registry?: (false | string) | undefined;
                        cwd?: string | undefined;
                        auth?: string | undefined;
                        install?: boolean | undefined;
                        silent?: boolean | undefined;
                    } | undefined;
                    install?: boolean | undefined;
                    auth?: string | undefined;
                } | undefined;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                cwd?: string | undefined;
                configFile?: string | undefined;
            } | undefined)[] | undefined;
            cwd?: string | undefined;
            sourceOptions?: {
                [x: string]: any;
                meta?: {
                    [x: string]: any;
                    name?: string | undefined;
                } | undefined;
                overrides?: {
                    manifest?: {
                        [x: string]: any;
                        content_scripts?: ({
                            matches?: (string | undefined)[] | undefined;
                            exclude_matches?: (string | undefined)[] | undefined;
                            css?: (string | undefined)[] | undefined;
                            js?: (string | undefined)[] | undefined;
                            run_at?: string | undefined;
                            all_frames?: boolean | undefined;
                            match_about_blank?: boolean | undefined;
                            include_globs?: (string | undefined)[] | undefined;
                            exclude_globs?: (string | undefined)[] | undefined;
                            world?: "ISOLATED" | "MAIN" | undefined;
                        } | undefined)[] | undefined;
                        content_security_policy?: {
                            extension_pages?: string | undefined;
                            sandbox?: string | undefined;
                        } | undefined;
                        host_permissions?: (string | undefined)[] | undefined;
                        optional_permissions?: (chrome.runtime.ManifestPermissions | undefined)[] | undefined;
                        optional_host_permissions?: (string | undefined)[] | undefined;
                        name?: string | undefined;
                        version?: string | undefined;
                        default_locale?: string | undefined;
                        description?: string | undefined;
                        icons?: {
                            [x: number]: string | undefined;
                        } | undefined;
                        author?: {
                            email?: string | undefined;
                        } | undefined;
                        background_page?: string | undefined;
                        chrome_settings_overrides?: {
                            homepage?: string | undefined;
                            search_provider?: {
                                name?: string | undefined;
                                keyword?: string | undefined;
                                favicon_url?: string | undefined;
                                search_url?: string | undefined;
                                encoding?: string | undefined;
                                suggest_url?: string | undefined;
                                instant_url?: string | undefined;
                                image_url?: string | undefined;
                                search_url_post_params?: string | undefined;
                                suggest_url_post_params?: string | undefined;
                                instant_url_post_params?: string | undefined;
                                image_url_post_params?: string | undefined;
                                alternate_urls?: (string | undefined)[] | undefined;
                                prepopulated_id?: number | undefined;
                                is_default?: boolean | undefined;
                            } | undefined;
                            startup_pages?: (string | undefined)[] | undefined;
                        } | undefined;
                        chrome_ui_overrides?: {
                            bookmarks_ui?: {
                                remove_bookmark_shortcut?: boolean | undefined;
                                remove_button?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        commands?: {
                            [x: string]: {
                                suggested_key?: {
                                    default?: string | undefined;
                                    windows?: string | undefined;
                                    mac?: string | undefined;
                                    chromeos?: string | undefined;
                                    linux?: string | undefined;
                                } | undefined;
                                description?: string | undefined;
                                global?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        content_capabilities?: {
                            matches?: (string | undefined)[] | undefined;
                            permissions?: (string | undefined)[] | undefined;
                        } | undefined;
                        converted_from_user_script?: boolean | undefined;
                        current_locale?: string | undefined;
                        event_rules?: ({
                            event?: string | undefined;
                            actions?: ({
                                type?: string | undefined;
                            } | undefined)[] | undefined;
                            conditions?: ({
                                pageUrl?: {
                                    hostContains?: string | undefined;
                                    hostEquals?: string | undefined;
                                    hostPrefix?: string | undefined;
                                    hostSuffix?: string | undefined;
                                    pathContains?: string | undefined;
                                    pathEquals?: string | undefined;
                                    pathPrefix?: string | undefined;
                                    pathSuffix?: string | undefined;
                                    queryContains?: string | undefined;
                                    queryEquals?: string | undefined;
                                    queryPrefix?: string | undefined;
                                    querySuffix?: string | undefined;
                                    urlContains?: string | undefined;
                                    urlEquals?: string | undefined;
                                    urlMatches?: string | undefined;
                                    originAndPathMatches?: string | undefined;
                                    urlPrefix?: string | undefined;
                                    urlSuffix?: string | undefined;
                                    schemes?: (string | undefined)[] | undefined;
                                    ports?: (number | (number | undefined)[] | undefined)[] | undefined;
                                } | undefined;
                                css?: (string | undefined)[] | undefined;
                                isBookmarked?: boolean | undefined;
                            } | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        externally_connectable?: {
                            ids?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                            accepts_tls_channel_id?: boolean | undefined;
                        } | undefined;
                        file_browser_handlers?: ({
                            id?: string | undefined;
                            default_title?: string | undefined;
                            file_filters?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                        file_system_provider_capabilities?: {
                            configurable?: boolean | undefined;
                            watchable?: boolean | undefined;
                            multiple_mounts?: boolean | undefined;
                            source?: string | undefined;
                        } | undefined;
                        homepage_url?: string | undefined;
                        import?: ({
                            id?: string | undefined;
                            minimum_version?: string | undefined;
                        } | undefined)[] | undefined;
                        export?: {
                            whitelist?: (string | undefined)[] | undefined;
                        } | undefined;
                        incognito?: string | undefined;
                        input_components?: ({
                            name?: string | undefined;
                            type?: string | undefined;
                            id?: string | undefined;
                            description?: string | undefined;
                            language?: string | (string | undefined)[] | undefined;
                            layouts?: (string | undefined)[] | undefined;
                            indicator?: string | undefined;
                        } | undefined)[] | undefined;
                        key?: string | undefined;
                        minimum_chrome_version?: string | undefined;
                        nacl_modules?: ({
                            path?: string | undefined;
                            mime_type?: string | undefined;
                        } | undefined)[] | undefined;
                        oauth2?: {
                            client_id?: string | undefined;
                            scopes?: (string | undefined)[] | undefined;
                        } | undefined;
                        offline_enabled?: boolean | undefined;
                        omnibox?: {
                            keyword?: string | undefined;
                        } | undefined;
                        platforms?: ({
                            nacl_arch?: string | undefined;
                            sub_package_path?: string | undefined;
                        } | undefined)[] | undefined;
                        plugins?: ({
                            path?: string | undefined;
                        } | undefined)[] | undefined;
                        requirements?: {
                            "3D"?: {
                                features?: (string | undefined)[] | undefined;
                            } | undefined;
                            plugins?: {
                                npapi?: boolean | undefined;
                            } | undefined;
                        } | undefined;
                        short_name?: string | undefined;
                        spellcheck?: {
                            dictionary_language?: string | undefined;
                            dictionary_locale?: string | undefined;
                            dictionary_format?: string | undefined;
                            dictionary_path?: string | undefined;
                        } | undefined;
                        storage?: {
                            managed_schema?: string | undefined;
                        } | undefined;
                        tts_engine?: {
                            voices?: ({
                                voice_name?: string | undefined;
                                lang?: string | undefined;
                                gender?: string | undefined;
                                event_types?: (string | undefined)[] | undefined;
                            } | undefined)[] | undefined;
                        } | undefined;
                        update_url?: string | undefined;
                        version_name?: string | undefined;
                        action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        page_action?: {
                            default_icon?: {
                                [x: number]: string | undefined;
                            } | undefined;
                            default_title?: string | undefined;
                            default_popup?: string | undefined;
                            browser_style?: boolean | undefined;
                        } | undefined;
                        browser_specific_settings?: {
                            gecko?: {
                                id?: string | undefined;
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                                update_url?: string | undefined;
                            } | undefined;
                            gecko_android?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                            safari?: {
                                strict_min_version?: string | undefined;
                                strict_max_version?: string | undefined;
                            } | undefined;
                        } | undefined;
                        permissions?: (chrome.runtime.ManifestPermissions | {
                            readonly [x: number]: string | undefined;
                            toString?: {} | undefined;
                            charAt?: {} | undefined;
                            charCodeAt?: {} | undefined;
                            concat?: {} | undefined;
                            indexOf?: {} | undefined;
                            lastIndexOf?: {} | undefined;
                            localeCompare?: {} | undefined;
                            match?: {} | undefined;
                            replace?: {} | undefined;
                            search?: {} | undefined;
                            slice?: {} | undefined;
                            split?: {} | undefined;
                            substring?: {} | undefined;
                            toLowerCase?: {} | undefined;
                            toLocaleLowerCase?: {} | undefined;
                            toUpperCase?: {} | undefined;
                            toLocaleUpperCase?: {} | undefined;
                            trim?: {} | undefined;
                            readonly length?: number | undefined;
                            substr?: {} | undefined;
                            valueOf?: {} | undefined;
                            codePointAt?: {} | undefined;
                            includes?: {} | undefined;
                            endsWith?: {} | undefined;
                            normalize?: {} | undefined;
                            repeat?: {} | undefined;
                            startsWith?: {} | undefined;
                            anchor?: {} | undefined;
                            big?: {} | undefined;
                            blink?: {} | undefined;
                            bold?: {} | undefined;
                            fixed?: {} | undefined;
                            fontcolor?: {} | undefined;
                            fontsize?: {} | undefined;
                            italics?: {} | undefined;
                            link?: {} | undefined;
                            small?: {} | undefined;
                            strike?: {} | undefined;
                            sub?: {} | undefined;
                            sup?: {} | undefined;
                            padStart?: {} | undefined;
                            padEnd?: {} | undefined;
                            trimEnd?: {} | undefined;
                            trimStart?: {} | undefined;
                            trimLeft?: {} | undefined;
                            trimRight?: {} | undefined;
                            matchAll?: {} | undefined;
                            [Symbol.iterator]?: {} | undefined;
                            at?: {} | undefined;
                        } | undefined)[] | undefined;
                        web_accessible_resources?: (string | undefined)[] | ({
                            resources?: (string | undefined)[] | undefined;
                            matches?: (string | undefined)[] | undefined;
                        } | undefined)[] | undefined;
                    } | {
                        then?: {} | undefined;
                        catch?: {} | undefined;
                        finally?: {} | undefined;
                        readonly [Symbol.toStringTag]?: string | undefined;
                    } | {} | undefined;
                    vite?: {} | undefined;
                    zip?: {
                        artifactTemplate?: string | undefined;
                        zipSources?: boolean | undefined;
                        sourcesTemplate?: string | undefined;
                        name?: string | undefined;
                        sourcesRoot?: string | undefined;
                        includeSources?: (string | undefined)[] | undefined;
                        excludeSources?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        downloadPackages?: (string | undefined)[] | undefined;
                        compressionLevel?: (0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9) | undefined;
                    } | undefined;
                    root?: string | undefined;
                    mode?: string | undefined;
                    dev?: {
                        server?: {
                            port?: number | undefined;
                            hostname?: string | undefined;
                        } | undefined;
                        reloadCommand?: (string | false) | undefined;
                    } | undefined;
                    publicDir?: string | undefined;
                    experimental?: {} | undefined;
                    srcDir?: string | undefined;
                    entrypointsDir?: string | undefined;
                    modulesDir?: string | undefined;
                    filterEntrypoints?: (string | undefined)[] | undefined;
                    outDir?: string | undefined;
                    outDirTemplate?: string | undefined;
                    debug?: boolean | undefined;
                    imports?: false | {
                        imports?: ({
                            name?: string | undefined;
                            as?: import("unimport").ImportName | undefined;
                            with?: {
                                [x: string]: string | undefined;
                            } | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | undefined)[] | undefined;
                        presets?: ("@vue/composition-api" | "@vueuse/core" | "@vueuse/head" | "pinia" | "preact" | "quasar" | "react" | "react-router" | "react-router-dom" | "svelte" | "svelte/animate" | "svelte/easing" | "svelte/motion" | "svelte/store" | "svelte/transition" | "vee-validate" | "vitepress" | "vue-demi" | "vue-i18n" | "vue-router" | "vue-router-composables" | "vue" | "vue/macros" | "vuex" | "vitest" | "uni-app" | "solid-js" | "solid-app-router" | "rxjs" | "date-fns" | {
                            imports?: (string | any | {
                                meta?: {
                                    [x: string]: any;
                                    description?: string | undefined;
                                    docsUrl?: string | undefined;
                                } | undefined;
                                name?: string | undefined;
                                type?: boolean | undefined;
                                as?: import("unimport").ImportName | undefined;
                                with?: {
                                    [x: string]: string | undefined;
                                } | undefined;
                                priority?: number | undefined;
                                disabled?: boolean | undefined;
                                dtsDisabled?: boolean | undefined;
                                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                                typeFrom?: import("unimport").ModuleId | undefined;
                            } | [name?: string | undefined, as?: string | undefined, from?: string | undefined] | undefined)[] | undefined;
                            from?: string | undefined;
                            priority?: number | undefined;
                            disabled?: boolean | undefined;
                            dtsDisabled?: boolean | undefined;
                            declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                            meta?: {
                                [x: string]: any;
                                description?: string | undefined;
                                docsUrl?: string | undefined;
                            } | undefined;
                            type?: boolean | undefined;
                            typeFrom?: import("unimport").ModuleId | undefined;
                        } | {
                            package?: string | undefined;
                            url?: string | undefined;
                            ignore?: (string | {
                                exec?: {} | undefined;
                                test?: {} | undefined;
                                readonly source?: string | undefined;
                                readonly global?: boolean | undefined;
                                readonly ignoreCase?: boolean | undefined;
                                readonly multiline?: boolean | undefined;
                                lastIndex?: number | undefined;
                                compile?: {} | undefined;
                                readonly flags?: string | undefined;
                                readonly sticky?: boolean | undefined;
                                readonly unicode?: boolean | undefined;
                                readonly dotAll?: boolean | undefined;
                                [Symbol.match]?: {} | undefined;
                                [Symbol.replace]?: {} | undefined;
                                [Symbol.search]?: {} | undefined;
                                [Symbol.split]?: {} | undefined;
                                [Symbol.matchAll]?: {} | undefined;
                            } | {} | undefined)[] | undefined;
                            cache?: boolean | undefined;
                        } | undefined)[] | undefined;
                        warn?: {} | undefined;
                        debugLog?: {} | undefined;
                        addons?: {
                            addons?: ({
                                name?: string | undefined;
                                transform?: {} | undefined;
                                declaration?: {} | undefined;
                                matchImports?: {} | undefined;
                                extendImports?: {} | undefined;
                                injectImportsResolved?: {} | undefined;
                                injectImportsStringified?: {} | undefined;
                            } | undefined)[] | undefined;
                            vueTemplate?: boolean | undefined;
                            vueDirectives?: true | {
                                isDirective?: {} | undefined;
                            } | undefined;
                        } | ({
                            name?: string | undefined;
                            transform?: {} | undefined;
                            declaration?: {} | undefined;
                            matchImports?: {} | undefined;
                            extendImports?: {} | undefined;
                            injectImportsResolved?: {} | undefined;
                            injectImportsStringified?: {} | undefined;
                        } | undefined)[] | undefined;
                        virtualImports?: (string | undefined)[] | undefined;
                        dirs?: (string | undefined)[] | undefined;
                        dirsScanOptions?: {
                            filePatterns?: (string | undefined)[] | undefined;
                            fileFilter?: {} | undefined;
                            types?: boolean | undefined;
                            cwd?: string | undefined;
                        } | undefined;
                        resolveId?: {} | undefined;
                        commentsDisable?: (string | undefined)[] | undefined;
                        commentsDebug?: (string | undefined)[] | undefined;
                        collectMeta?: boolean | undefined;
                        injectAtEnd?: boolean | undefined;
                        mergeExisting?: boolean | undefined;
                        parser?: ("acorn" | "regex") | undefined;
                        eslintrc?: {
                            enabled?: (false | true | "auto" | 8 | 9) | undefined;
                            filePath?: string | undefined;
                            globalsPropValue?: import("../../../types").EslintGlobalsPropValue | undefined;
                        } | undefined;
                    } | undefined;
                    browser?: import("../../../types").TargetBrowser | undefined;
                    manifestVersion?: import("../../../types").TargetManifestVersion | undefined;
                    logger?: {
                        debug?: {} | undefined;
                        log?: {} | undefined;
                        info?: {} | undefined;
                        warn?: {} | undefined;
                        error?: {} | undefined;
                        fatal?: {} | undefined;
                        success?: {} | undefined;
                        level?: 0 | 1 | 2 | 3 | 4 | 5 | {
                            toString?: {} | undefined;
                            toFixed?: {} | undefined;
                            toExponential?: {} | undefined;
                            toPrecision?: {} | undefined;
                            valueOf?: {} | undefined;
                            toLocaleString?: {} | undefined;
                        } | undefined;
                    } | undefined;
                    runner?: {
                        disabled?: boolean | undefined;
                        openConsole?: boolean | undefined;
                        openDevtools?: boolean | undefined;
                        binaries?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxProfile?: string | undefined;
                        chromiumProfile?: string | undefined;
                        chromiumPref?: {
                            [x: string]: any;
                        } | undefined;
                        chromiumPort?: number | undefined;
                        firefoxPrefs?: {
                            [x: string]: string | undefined;
                        } | undefined;
                        firefoxArgs?: (string | undefined)[] | undefined;
                        chromiumArgs?: (string | undefined)[] | undefined;
                        startUrls?: (string | undefined)[] | undefined;
                        keepProfileChanges?: boolean | undefined;
                    } | undefined;
                    transformManifest?: {} | undefined;
                    analysis?: {
                        enabled?: boolean | undefined;
                        open?: boolean | undefined;
                        template?: import("@aklinker1/rollup-plugin-visualizer").PluginVisualizerOptions["template"];
                        outputFile?: string | undefined;
                        keepArtifacts?: boolean | undefined;
                    } | undefined;
                    alias?: {
                        [x: string]: string | undefined;
                    } | undefined;
                    extensionApi?: ("webextension-polyfill" | "chrome") | undefined;
                    entrypointLoader?: ("vite-node" | "jiti") | undefined;
                    hooks?: {
                        'vite:build:extendConfig'?: {} | undefined;
                        'vite:devServer:extendConfig'?: {} | undefined;
                        ready?: {} | undefined;
                        'config:resolved'?: {} | undefined;
                        'prepare:types'?: {} | undefined;
                        'prepare:publicPaths'?: {} | undefined;
                        'build:before'?: {} | undefined;
                        'build:done'?: {} | undefined;
                        'build:manifestGenerated'?: {} | undefined;
                        'entrypoints:found'?: {} | undefined;
                        'entrypoints:resolved'?: {} | undefined;
                        'entrypoints:grouped'?: {} | undefined;
                        'build:publicAssets'?: {} | undefined;
                        'zip:start'?: {} | undefined;
                        'zip:extension:start'?: {} | undefined;
                        'zip:extension:done'?: {} | undefined;
                        'zip:sources:start'?: {} | undefined;
                        'zip:sources:done'?: {} | undefined;
                        'zip:done'?: {} | undefined;
                        'server:created'?: {} | undefined;
                        'server:started'?: {} | undefined;
                        'server:closed'?: {} | undefined;
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                    } | {
                        vite?: {
                            "build:extendConfig"?: {} | undefined;
                            "devServer:extendConfig"?: {} | undefined;
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | {
                            build?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                            devServer?: {
                                extendConfig?: {} | undefined;
                            } | {
                                extendConfig?: {} | undefined;
                            } | undefined;
                        } | undefined;
                        config?: {
                            resolved?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                        } | undefined;
                        prepare?: {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | {
                            types?: {} | undefined;
                            publicPaths?: {} | undefined;
                        } | undefined;
                        build?: {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | {
                            before?: {} | undefined;
                            done?: {} | undefined;
                            manifestGenerated?: {} | undefined;
                            publicAssets?: {} | undefined;
                        } | undefined;
                        entrypoints?: {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | {
                            resolved?: {} | undefined;
                            found?: {} | undefined;
                            grouped?: {} | undefined;
                        } | undefined;
                        zip?: {
                            done?: {} | undefined;
                            start?: {} | undefined;
                            "extension:start"?: {} | undefined;
                            "extension:done"?: {} | undefined;
                            "sources:start"?: {} | undefined;
                            "sources:done"?: {} | undefined;
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                        } | {
                            extension?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            sources?: {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | {
                                done?: {} | undefined;
                                start?: {} | undefined;
                            } | undefined;
                            done?: {} | undefined;
                            start?: {} | undefined;
                        } | undefined;
                        server?: {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | {
                            closed?: {} | undefined;
                            created?: {} | undefined;
                            started?: {} | undefined;
                        } | undefined;
                        ready?: {} | undefined;
                    } | undefined;
                    modules?: (string | undefined)[] | undefined;
                } | undefined;
                giget?: {
                    provider?: string | undefined;
                    force?: boolean | undefined;
                    forceClean?: boolean | undefined;
                    offline?: boolean | undefined;
                    preferOffline?: boolean | undefined;
                    providers?: {
                        [x: string]: {} | undefined;
                    } | undefined;
                    dir?: string | undefined;
                    registry?: (false | string) | undefined;
                    cwd?: string | undefined;
                    auth?: string | undefined;
                    install?: boolean | undefined;
                    silent?: boolean | undefined;
                } | undefined;
                install?: boolean | undefined;
                auth?: string | undefined;
            } | undefined;
        } | undefined;
        alias?: {
            [x: string]: string | undefined;
        } | undefined;
        extensionApi?: "webextension-polyfill" | "chrome" | undefined;
        entrypointLoader?: "vite-node" | "jiti" | undefined;
        experimental?: {} | undefined;
        dev?: {
            server?: {
                port?: number | undefined;
                hostname?: string | undefined;
                watchDebounce?: number | undefined;
            } | undefined;
            reloadCommand?: string | false | undefined;
        } | undefined;
        hooks?: {
            'vite:build:extendConfig'?: {} | undefined;
            'vite:devServer:extendConfig'?: {} | undefined;
            ready?: {} | undefined;
            'config:resolved'?: {} | undefined;
            'prepare:types'?: {} | undefined;
            'prepare:publicPaths'?: {} | undefined;
            'build:before'?: {} | undefined;
            'build:done'?: {} | undefined;
            'build:manifestGenerated'?: {} | undefined;
            'entrypoints:found'?: {} | undefined;
            'entrypoints:resolved'?: {} | undefined;
            'entrypoints:grouped'?: {} | undefined;
            'build:publicAssets'?: {} | undefined;
            'zip:start'?: {} | undefined;
            'zip:extension:start'?: {} | undefined;
            'zip:extension:done'?: {} | undefined;
            'zip:sources:start'?: {} | undefined;
            'zip:sources:done'?: {} | undefined;
            'zip:done'?: {} | undefined;
            'server:created'?: {} | undefined;
            'server:started'?: {} | undefined;
            'server:closed'?: {} | undefined;
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
        } | {
            vite?: {
                "build:extendConfig"?: {} | undefined;
                "devServer:extendConfig"?: {} | undefined;
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | {
                build?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
                devServer?: {
                    extendConfig?: {} | undefined;
                } | {
                    extendConfig?: {} | undefined;
                } | undefined;
            } | undefined;
            config?: {
                resolved?: {} | undefined;
            } | {
                resolved?: {} | undefined;
            } | undefined;
            prepare?: {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | {
                types?: {} | undefined;
                publicPaths?: {} | undefined;
            } | undefined;
            build?: {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | {
                before?: {} | undefined;
                done?: {} | undefined;
                manifestGenerated?: {} | undefined;
                publicAssets?: {} | undefined;
            } | undefined;
            entrypoints?: {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | {
                resolved?: {} | undefined;
                found?: {} | undefined;
                grouped?: {} | undefined;
            } | undefined;
            zip?: {
                done?: {} | undefined;
                start?: {} | undefined;
                "extension:start"?: {} | undefined;
                "extension:done"?: {} | undefined;
                "sources:start"?: {} | undefined;
                "sources:done"?: {} | undefined;
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
            } | {
                extension?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                sources?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | {
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                done?: {} | undefined;
                start?: {} | undefined;
            } | undefined;
            server?: {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | {
                closed?: {} | undefined;
                created?: {} | undefined;
                started?: {} | undefined;
            } | undefined;
            ready?: {} | undefined;
        } | undefined;
        builtinModules?: ({
            name?: string | undefined;
            configKey?: string | undefined;
            imports?: ({
                name?: string | undefined;
                as?: import("unimport").ImportName | undefined;
                with?: {
                    [x: string]: string | undefined;
                } | undefined;
                from?: string | undefined;
                priority?: number | undefined;
                disabled?: boolean | undefined;
                dtsDisabled?: boolean | undefined;
                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                meta?: {
                    [x: string]: any;
                    description?: string | undefined;
                    docsUrl?: string | undefined;
                } | undefined;
                type?: boolean | undefined;
                typeFrom?: import("unimport").ModuleId | undefined;
            } | undefined)[] | undefined;
            hooks?: {
                'vite:build:extendConfig'?: {} | undefined;
                'vite:devServer:extendConfig'?: {} | undefined;
                ready?: {} | undefined;
                'config:resolved'?: {} | undefined;
                'prepare:types'?: {} | undefined;
                'prepare:publicPaths'?: {} | undefined;
                'build:before'?: {} | undefined;
                'build:done'?: {} | undefined;
                'build:manifestGenerated'?: {} | undefined;
                'entrypoints:found'?: {} | undefined;
                'entrypoints:resolved'?: {} | undefined;
                'entrypoints:grouped'?: {} | undefined;
                'build:publicAssets'?: {} | undefined;
                'zip:start'?: {} | undefined;
                'zip:extension:start'?: {} | undefined;
                'zip:extension:done'?: {} | undefined;
                'zip:sources:start'?: {} | undefined;
                'zip:sources:done'?: {} | undefined;
                'zip:done'?: {} | undefined;
                'server:created'?: {} | undefined;
                'server:started'?: {} | undefined;
                'server:closed'?: {} | undefined;
                vite?: {
                    "build:extendConfig"?: {} | undefined;
                    "devServer:extendConfig"?: {} | undefined;
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | {
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | undefined;
                config?: {
                    resolved?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                } | undefined;
                prepare?: {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | undefined;
                build?: {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | undefined;
                entrypoints?: {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | undefined;
                zip?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                    "extension:start"?: {} | undefined;
                    "extension:done"?: {} | undefined;
                    "sources:start"?: {} | undefined;
                    "sources:done"?: {} | undefined;
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                } | {
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                server?: {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | undefined;
            } | {
                vite?: {
                    "build:extendConfig"?: {} | undefined;
                    "devServer:extendConfig"?: {} | undefined;
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | {
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | undefined;
                config?: {
                    resolved?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                } | undefined;
                prepare?: {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | undefined;
                build?: {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | undefined;
                entrypoints?: {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | undefined;
                zip?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                    "extension:start"?: {} | undefined;
                    "extension:done"?: {} | undefined;
                    "sources:start"?: {} | undefined;
                    "sources:done"?: {} | undefined;
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                } | {
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                server?: {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | undefined;
                ready?: {} | undefined;
            } | undefined;
            setup?: {} | undefined;
        } | undefined)[] | undefined;
        userModules?: ({
            type?: "local" | "node_module" | undefined;
            id?: string | undefined;
            name?: string | undefined;
            configKey?: string | undefined;
            imports?: ({
                name?: string | undefined;
                as?: import("unimport").ImportName | undefined;
                with?: {
                    [x: string]: string | undefined;
                } | undefined;
                from?: string | undefined;
                priority?: number | undefined;
                disabled?: boolean | undefined;
                dtsDisabled?: boolean | undefined;
                declarationType?: "function" | "var" | "let" | "const" | "enum" | "const enum" | "class" | "async function" | undefined;
                meta?: {
                    [x: string]: any;
                    description?: string | undefined;
                    docsUrl?: string | undefined;
                } | undefined;
                type?: boolean | undefined;
                typeFrom?: import("unimport").ModuleId | undefined;
            } | undefined)[] | undefined;
            hooks?: {
                'vite:build:extendConfig'?: {} | undefined;
                'vite:devServer:extendConfig'?: {} | undefined;
                ready?: {} | undefined;
                'config:resolved'?: {} | undefined;
                'prepare:types'?: {} | undefined;
                'prepare:publicPaths'?: {} | undefined;
                'build:before'?: {} | undefined;
                'build:done'?: {} | undefined;
                'build:manifestGenerated'?: {} | undefined;
                'entrypoints:found'?: {} | undefined;
                'entrypoints:resolved'?: {} | undefined;
                'entrypoints:grouped'?: {} | undefined;
                'build:publicAssets'?: {} | undefined;
                'zip:start'?: {} | undefined;
                'zip:extension:start'?: {} | undefined;
                'zip:extension:done'?: {} | undefined;
                'zip:sources:start'?: {} | undefined;
                'zip:sources:done'?: {} | undefined;
                'zip:done'?: {} | undefined;
                'server:created'?: {} | undefined;
                'server:started'?: {} | undefined;
                'server:closed'?: {} | undefined;
                vite?: {
                    "build:extendConfig"?: {} | undefined;
                    "devServer:extendConfig"?: {} | undefined;
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | {
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | undefined;
                config?: {
                    resolved?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                } | undefined;
                prepare?: {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | undefined;
                build?: {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | undefined;
                entrypoints?: {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | undefined;
                zip?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                    "extension:start"?: {} | undefined;
                    "extension:done"?: {} | undefined;
                    "sources:start"?: {} | undefined;
                    "sources:done"?: {} | undefined;
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                } | {
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                server?: {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | undefined;
            } | {
                vite?: {
                    "build:extendConfig"?: {} | undefined;
                    "devServer:extendConfig"?: {} | undefined;
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | {
                    build?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                    devServer?: {
                        extendConfig?: {} | undefined;
                    } | {
                        extendConfig?: {} | undefined;
                    } | undefined;
                } | undefined;
                config?: {
                    resolved?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                } | undefined;
                prepare?: {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | {
                    types?: {} | undefined;
                    publicPaths?: {} | undefined;
                } | undefined;
                build?: {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | {
                    before?: {} | undefined;
                    done?: {} | undefined;
                    manifestGenerated?: {} | undefined;
                    publicAssets?: {} | undefined;
                } | undefined;
                entrypoints?: {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | {
                    resolved?: {} | undefined;
                    found?: {} | undefined;
                    grouped?: {} | undefined;
                } | undefined;
                zip?: {
                    done?: {} | undefined;
                    start?: {} | undefined;
                    "extension:start"?: {} | undefined;
                    "extension:done"?: {} | undefined;
                    "sources:start"?: {} | undefined;
                    "sources:done"?: {} | undefined;
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                } | {
                    extension?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    sources?: {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | {
                        done?: {} | undefined;
                        start?: {} | undefined;
                    } | undefined;
                    done?: {} | undefined;
                    start?: {} | undefined;
                } | undefined;
                server?: {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | {
                    closed?: {} | undefined;
                    created?: {} | undefined;
                    started?: {} | undefined;
                } | undefined;
                ready?: {} | undefined;
            } | undefined;
            setup?: {} | undefined;
        } | undefined)[] | undefined;
        plugins?: (string | undefined)[] | undefined;
    } | undefined;
    hooks?: {
        hook?: {} | undefined;
        hookOnce?: {} | undefined;
        removeHook?: {} | undefined;
        deprecateHook?: {} | undefined;
        deprecateHooks?: {} | undefined;
        addHooks?: {} | undefined;
        removeHooks?: {} | undefined;
        removeAllHooks?: {} | undefined;
        callHook?: {} | undefined;
        callHookParallel?: {} | undefined;
        callHookWith?: {} | undefined;
        beforeEach?: {} | undefined;
        afterEach?: {} | undefined;
    } | undefined;
    hook?: {} | undefined;
    logger?: {
        debug?: {} | undefined;
        log?: {} | undefined;
        info?: {} | undefined;
        warn?: {} | undefined;
        error?: {} | undefined;
        fatal?: {} | undefined;
        success?: {} | undefined;
        level?: 0 | 1 | 2 | 3 | 4 | 5 | {
            toString?: {} | undefined;
            toFixed?: {} | undefined;
            toExponential?: {} | undefined;
            toPrecision?: {} | undefined;
            valueOf?: {} | undefined;
            toLocaleString?: {} | undefined;
        } | undefined;
    } | undefined;
    reloadConfig?: {} | undefined;
    pm?: {
        addDependency?: {} | undefined;
        addDevDependency?: {} | undefined;
        ensureDependencyInstalled?: {} | undefined;
        installDependencies?: {} | undefined;
        removeDependency?: {} | undefined;
        downloadDependency?: {} | undefined;
        listDependencies?: {} | undefined;
        overridesKey?: string | undefined;
        name?: import("nypm").PackageManagerName | undefined;
        command?: string | undefined;
        version?: string | undefined;
        majorVersion?: string | undefined;
        lockFile?: string | (string | undefined)[] | undefined;
        files?: (string | undefined)[] | undefined;
    } | undefined;
    server?: {
        currentOutput?: {
            manifest?: any;
            publicAssets?: ({
                type?: "asset" | undefined;
                fileName?: string | undefined;
            } | undefined)[] | undefined;
            steps?: ({
                entrypoints?: {
                    type?: "popup" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        mv2Key?: "browser_action" | "page_action" | undefined;
                        defaultTitle?: string | undefined;
                        browserStyle?: boolean | undefined;
                        defaultIcon?: {
                            [x: string]: string | undefined;
                        } | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "content-script" | undefined;
                    options?: {
                        world?: "ISOLATED" | "MAIN" | undefined;
                        matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                        runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                        matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                        excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                        includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                        excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                        allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                        matchOriginAsFallback?: boolean | undefined;
                        cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                        registration?: "manifest" | "runtime" | undefined;
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "background" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        persistent?: boolean | undefined;
                        type?: "module" | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "options" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        browserStyle?: boolean | undefined;
                        openInTab?: boolean | undefined;
                        chromeStyle?: boolean | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "sidepanel" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        defaultTitle?: string | undefined;
                        browserStyle?: boolean | undefined;
                        openAtInstall?: boolean | undefined;
                        defaultIcon?: string | {
                            [x: string]: string | undefined;
                        } | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | ({
                    type?: "popup" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        mv2Key?: "browser_action" | "page_action" | undefined;
                        defaultTitle?: string | undefined;
                        browserStyle?: boolean | undefined;
                        defaultIcon?: {
                            [x: string]: string | undefined;
                        } | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "content-script" | undefined;
                    options?: {
                        world?: "ISOLATED" | "MAIN" | undefined;
                        matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                        runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                        matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                        excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                        includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                        excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                        allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                        matchOriginAsFallback?: boolean | undefined;
                        cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                        registration?: "manifest" | "runtime" | undefined;
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "background" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        persistent?: boolean | undefined;
                        type?: "module" | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "options" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        browserStyle?: boolean | undefined;
                        openInTab?: boolean | undefined;
                        chromeStyle?: boolean | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | {
                    type?: "sidepanel" | undefined;
                    options?: {
                        include?: (string | undefined)[] | undefined;
                        exclude?: (string | undefined)[] | undefined;
                        defaultTitle?: string | undefined;
                        browserStyle?: boolean | undefined;
                        openAtInstall?: boolean | undefined;
                        defaultIcon?: string | {
                            [x: string]: string | undefined;
                        } | undefined;
                    } | undefined;
                    name?: string | undefined;
                    inputPath?: string | undefined;
                    outputDir?: string | undefined;
                    skipped?: boolean | undefined;
                } | undefined)[] | undefined;
                chunks?: ({
                    type?: "asset" | undefined;
                    fileName?: string | undefined;
                } | {
                    type?: "chunk" | undefined;
                    fileName?: string | undefined;
                    moduleIds?: (string | undefined)[] | undefined;
                } | undefined)[] | undefined;
            } | undefined)[] | undefined;
        } | undefined;
        start?: {} | undefined;
        stop?: {} | undefined;
        restart?: {} | undefined;
        transformHtml?: {} | undefined;
        reloadExtension?: {} | undefined;
        reloadPage?: {} | undefined;
        reloadContentScript?: {} | undefined;
        restartBrowser?: {} | undefined;
        ws?: {
            send?: {} | undefined;
            on?: {} | undefined;
        } | undefined;
        watcher?: {
            options?: {
                persistent?: boolean | undefined;
                ignored?: string | {
                    exec?: {} | undefined;
                    test?: {} | undefined;
                    readonly source?: string | undefined;
                    readonly global?: boolean | undefined;
                    readonly ignoreCase?: boolean | undefined;
                    readonly multiline?: boolean | undefined;
                    lastIndex?: number | undefined;
                    compile?: {} | undefined;
                    readonly flags?: string | undefined;
                    readonly sticky?: boolean | undefined;
                    readonly unicode?: boolean | undefined;
                    readonly dotAll?: boolean | undefined;
                    [Symbol.match]?: {} | undefined;
                    [Symbol.replace]?: {} | undefined;
                    [Symbol.search]?: {} | undefined;
                    [Symbol.split]?: {} | undefined;
                    [Symbol.matchAll]?: {} | undefined;
                } | {} | (string | {
                    exec?: {} | undefined;
                    test?: {} | undefined;
                    readonly source?: string | undefined;
                    readonly global?: boolean | undefined;
                    readonly ignoreCase?: boolean | undefined;
                    readonly multiline?: boolean | undefined;
                    lastIndex?: number | undefined;
                    compile?: {} | undefined;
                    readonly flags?: string | undefined;
                    readonly sticky?: boolean | undefined;
                    readonly unicode?: boolean | undefined;
                    readonly dotAll?: boolean | undefined;
                    [Symbol.match]?: {} | undefined;
                    [Symbol.replace]?: {} | undefined;
                    [Symbol.search]?: {} | undefined;
                    [Symbol.split]?: {} | undefined;
                    [Symbol.matchAll]?: {} | undefined;
                } | {} | undefined)[] | undefined;
                ignoreInitial?: boolean | undefined;
                followSymlinks?: boolean | undefined;
                cwd?: string | undefined;
                disableGlobbing?: boolean | undefined;
                usePolling?: boolean | undefined;
                useFsEvents?: boolean | undefined;
                alwaysStat?: boolean | undefined;
                depth?: number | undefined;
                interval?: number | undefined;
                binaryInterval?: number | undefined;
                ignorePermissionErrors?: boolean | undefined;
                atomic?: (boolean | number) | undefined;
                awaitWriteFinish?: boolean | {
                    stabilityThreshold?: number | undefined;
                    pollInterval?: number | undefined;
                } | undefined;
            } | undefined;
            ref?: {} | undefined;
            unref?: {} | undefined;
            add?: {} | undefined;
            unwatch?: {} | undefined;
            getWatched?: {} | undefined;
            close?: {} | undefined;
            on?: {} | undefined;
            [EventEmitter.captureRejectionSymbol]?: {} | undefined;
            addListener?: {} | undefined;
            once?: {} | undefined;
            removeListener?: {} | undefined;
            off?: {} | undefined;
            removeAllListeners?: {} | undefined;
            setMaxListeners?: {} | undefined;
            getMaxListeners?: {} | undefined;
            listeners?: {} | undefined;
            rawListeners?: {} | undefined;
            emit?: {} | undefined;
            listenerCount?: {} | undefined;
            prependListener?: {} | undefined;
            prependOnceListener?: {} | undefined;
            eventNames?: {} | undefined;
        } | undefined;
        on?: {} | undefined;
        port?: number | undefined;
        hostname?: string | undefined;
        origin?: string | undefined;
    } | undefined;
    builder?: {
        name?: string | undefined;
        version?: string | undefined;
        importEntrypoint?: {} | undefined;
        importEntrypoints?: {} | undefined;
        build?: {} | undefined;
        createServer?: {} | undefined;
    } | undefined;
} | undefined) => Wxt;
export declare const fakeWxtDevServer: (overrides?: {
    currentOutput?: {
        manifest?: any;
        publicAssets?: ({
            type?: "asset" | undefined;
            fileName?: string | undefined;
        } | undefined)[] | undefined;
        steps?: ({
            entrypoints?: {
                type?: "popup" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    mv2Key?: "browser_action" | "page_action" | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    defaultIcon?: {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "content-script" | undefined;
                options?: {
                    world?: "ISOLATED" | "MAIN" | undefined;
                    matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                    runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                    matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                    excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                    includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                    excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                    allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                    matchOriginAsFallback?: boolean | undefined;
                    cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                    registration?: "manifest" | "runtime" | undefined;
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "background" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    persistent?: boolean | undefined;
                    type?: "module" | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "options" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    browserStyle?: boolean | undefined;
                    openInTab?: boolean | undefined;
                    chromeStyle?: boolean | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sidepanel" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    openAtInstall?: boolean | undefined;
                    defaultIcon?: string | {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | ({
                type?: "popup" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    mv2Key?: "browser_action" | "page_action" | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    defaultIcon?: {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "content-script" | undefined;
                options?: {
                    world?: "ISOLATED" | "MAIN" | undefined;
                    matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                    runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                    matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                    excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                    includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                    excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                    allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                    matchOriginAsFallback?: boolean | undefined;
                    cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                    registration?: "manifest" | "runtime" | undefined;
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "background" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    persistent?: boolean | undefined;
                    type?: "module" | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "options" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    browserStyle?: boolean | undefined;
                    openInTab?: boolean | undefined;
                    chromeStyle?: boolean | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sidepanel" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    openAtInstall?: boolean | undefined;
                    defaultIcon?: string | {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | undefined)[] | undefined;
            chunks?: ({
                type?: "asset" | undefined;
                fileName?: string | undefined;
            } | {
                type?: "chunk" | undefined;
                fileName?: string | undefined;
                moduleIds?: (string | undefined)[] | undefined;
            } | undefined)[] | undefined;
        } | undefined)[] | undefined;
    } | undefined;
    start?: {} | undefined;
    stop?: {} | undefined;
    restart?: {} | undefined;
    transformHtml?: {} | undefined;
    reloadExtension?: {} | undefined;
    reloadPage?: {} | undefined;
    reloadContentScript?: {} | undefined;
    restartBrowser?: {} | undefined;
    ws?: {
        send?: {} | undefined;
        on?: {} | undefined;
    } | undefined;
    watcher?: {
        options?: {
            persistent?: boolean | undefined;
            ignored?: string | {
                exec?: {} | undefined;
                test?: {} | undefined;
                readonly source?: string | undefined;
                readonly global?: boolean | undefined;
                readonly ignoreCase?: boolean | undefined;
                readonly multiline?: boolean | undefined;
                lastIndex?: number | undefined;
                compile?: {} | undefined;
                readonly flags?: string | undefined;
                readonly sticky?: boolean | undefined;
                readonly unicode?: boolean | undefined;
                readonly dotAll?: boolean | undefined;
                [Symbol.match]?: {} | undefined;
                [Symbol.replace]?: {} | undefined;
                [Symbol.search]?: {} | undefined;
                [Symbol.split]?: {} | undefined;
                [Symbol.matchAll]?: {} | undefined;
            } | {} | (string | {
                exec?: {} | undefined;
                test?: {} | undefined;
                readonly source?: string | undefined;
                readonly global?: boolean | undefined;
                readonly ignoreCase?: boolean | undefined;
                readonly multiline?: boolean | undefined;
                lastIndex?: number | undefined;
                compile?: {} | undefined;
                readonly flags?: string | undefined;
                readonly sticky?: boolean | undefined;
                readonly unicode?: boolean | undefined;
                readonly dotAll?: boolean | undefined;
                [Symbol.match]?: {} | undefined;
                [Symbol.replace]?: {} | undefined;
                [Symbol.search]?: {} | undefined;
                [Symbol.split]?: {} | undefined;
                [Symbol.matchAll]?: {} | undefined;
            } | {} | undefined)[] | undefined;
            ignoreInitial?: boolean | undefined;
            followSymlinks?: boolean | undefined;
            cwd?: string | undefined;
            disableGlobbing?: boolean | undefined;
            usePolling?: boolean | undefined;
            useFsEvents?: boolean | undefined;
            alwaysStat?: boolean | undefined;
            depth?: number | undefined;
            interval?: number | undefined;
            binaryInterval?: number | undefined;
            ignorePermissionErrors?: boolean | undefined;
            atomic?: (boolean | number) | undefined;
            awaitWriteFinish?: boolean | {
                stabilityThreshold?: number | undefined;
                pollInterval?: number | undefined;
            } | undefined;
        } | undefined;
        ref?: {} | undefined;
        unref?: {} | undefined;
        add?: {} | undefined;
        unwatch?: {} | undefined;
        getWatched?: {} | undefined;
        close?: {} | undefined;
        on?: {} | undefined;
        [EventEmitter.captureRejectionSymbol]?: {} | undefined;
        addListener?: {} | undefined;
        once?: {} | undefined;
        removeListener?: {} | undefined;
        off?: {} | undefined;
        removeAllListeners?: {} | undefined;
        setMaxListeners?: {} | undefined;
        getMaxListeners?: {} | undefined;
        listeners?: {} | undefined;
        rawListeners?: {} | undefined;
        emit?: {} | undefined;
        listenerCount?: {} | undefined;
        prependListener?: {} | undefined;
        prependOnceListener?: {} | undefined;
        eventNames?: {} | undefined;
    } | undefined;
    on?: {} | undefined;
    port?: number | undefined;
    hostname?: string | undefined;
    origin?: string | undefined;
} | undefined) => WxtDevServer;
export declare function setFakeWxt(overrides?: DeepPartial<Wxt>): Wxt;
export declare const fakeBuildOutput: (overrides?: {
    manifest?: any;
    publicAssets?: ({
        type?: "asset" | undefined;
        fileName?: string | undefined;
    } | undefined)[] | undefined;
    steps?: ({
        entrypoints?: {
            type?: "popup" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                mv2Key?: "browser_action" | "page_action" | undefined;
                defaultTitle?: string | undefined;
                browserStyle?: boolean | undefined;
                defaultIcon?: {
                    [x: string]: string | undefined;
                } | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "content-script" | undefined;
            options?: {
                world?: "ISOLATED" | "MAIN" | undefined;
                matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                matchOriginAsFallback?: boolean | undefined;
                cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                registration?: "manifest" | "runtime" | undefined;
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "background" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                persistent?: boolean | undefined;
                type?: "module" | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "options" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                browserStyle?: boolean | undefined;
                openInTab?: boolean | undefined;
                chromeStyle?: boolean | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "sidepanel" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                defaultTitle?: string | undefined;
                browserStyle?: boolean | undefined;
                openAtInstall?: boolean | undefined;
                defaultIcon?: string | {
                    [x: string]: string | undefined;
                } | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | ({
            type?: "popup" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                mv2Key?: "browser_action" | "page_action" | undefined;
                defaultTitle?: string | undefined;
                browserStyle?: boolean | undefined;
                defaultIcon?: {
                    [x: string]: string | undefined;
                } | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "content-script" | undefined;
            options?: {
                world?: "ISOLATED" | "MAIN" | undefined;
                matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                matchOriginAsFallback?: boolean | undefined;
                cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                registration?: "manifest" | "runtime" | undefined;
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "background" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                persistent?: boolean | undefined;
                type?: "module" | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "options" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                browserStyle?: boolean | undefined;
                openInTab?: boolean | undefined;
                chromeStyle?: boolean | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | {
            type?: "sidepanel" | undefined;
            options?: {
                include?: (string | undefined)[] | undefined;
                exclude?: (string | undefined)[] | undefined;
                defaultTitle?: string | undefined;
                browserStyle?: boolean | undefined;
                openAtInstall?: boolean | undefined;
                defaultIcon?: string | {
                    [x: string]: string | undefined;
                } | undefined;
            } | undefined;
            name?: string | undefined;
            inputPath?: string | undefined;
            outputDir?: string | undefined;
            skipped?: boolean | undefined;
        } | undefined)[] | undefined;
        chunks?: ({
            type?: "asset" | undefined;
            fileName?: string | undefined;
        } | {
            type?: "chunk" | undefined;
            fileName?: string | undefined;
            moduleIds?: (string | undefined)[] | undefined;
        } | undefined)[] | undefined;
    } | undefined)[] | undefined;
} | undefined) => BuildOutput;
export declare const fakeBuildStepOutput: (overrides?: {
    entrypoints?: {
        type?: "popup" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            mv2Key?: "browser_action" | "page_action" | undefined;
            defaultTitle?: string | undefined;
            browserStyle?: boolean | undefined;
            defaultIcon?: {
                [x: string]: string | undefined;
            } | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "content-script" | undefined;
        options?: {
            world?: "ISOLATED" | "MAIN" | undefined;
            matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
            runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
            matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
            excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
            includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
            excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
            allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
            matchOriginAsFallback?: boolean | undefined;
            cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
            registration?: "manifest" | "runtime" | undefined;
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "background" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            persistent?: boolean | undefined;
            type?: "module" | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "options" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            browserStyle?: boolean | undefined;
            openInTab?: boolean | undefined;
            chromeStyle?: boolean | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "sidepanel" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            defaultTitle?: string | undefined;
            browserStyle?: boolean | undefined;
            openAtInstall?: boolean | undefined;
            defaultIcon?: string | {
                [x: string]: string | undefined;
            } | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | ({
        type?: "popup" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            mv2Key?: "browser_action" | "page_action" | undefined;
            defaultTitle?: string | undefined;
            browserStyle?: boolean | undefined;
            defaultIcon?: {
                [x: string]: string | undefined;
            } | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "content-script" | undefined;
        options?: {
            world?: "ISOLATED" | "MAIN" | undefined;
            matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
            runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
            matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
            excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
            includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
            excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
            allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
            matchOriginAsFallback?: boolean | undefined;
            cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
            registration?: "manifest" | "runtime" | undefined;
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "background" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            persistent?: boolean | undefined;
            type?: "module" | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "options" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            browserStyle?: boolean | undefined;
            openInTab?: boolean | undefined;
            chromeStyle?: boolean | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | {
        type?: "sidepanel" | undefined;
        options?: {
            include?: (string | undefined)[] | undefined;
            exclude?: (string | undefined)[] | undefined;
            defaultTitle?: string | undefined;
            browserStyle?: boolean | undefined;
            openAtInstall?: boolean | undefined;
            defaultIcon?: string | {
                [x: string]: string | undefined;
            } | undefined;
        } | undefined;
        name?: string | undefined;
        inputPath?: string | undefined;
        outputDir?: string | undefined;
        skipped?: boolean | undefined;
    } | undefined)[] | undefined;
    chunks?: ({
        type?: "asset" | undefined;
        fileName?: string | undefined;
    } | {
        type?: "chunk" | undefined;
        fileName?: string | undefined;
        moduleIds?: (string | undefined)[] | undefined;
    } | undefined)[] | undefined;
} | undefined) => BuildStepOutput;
export declare const fakeManifestCommand: (overrides?: any) => Manifest.WebExtensionManifestCommandsType;
export declare const fakeDevServer: (overrides?: {
    currentOutput?: {
        manifest?: any;
        publicAssets?: ({
            type?: "asset" | undefined;
            fileName?: string | undefined;
        } | undefined)[] | undefined;
        steps?: ({
            entrypoints?: {
                type?: "popup" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    mv2Key?: "browser_action" | "page_action" | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    defaultIcon?: {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "content-script" | undefined;
                options?: {
                    world?: "ISOLATED" | "MAIN" | undefined;
                    matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                    runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                    matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                    excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                    includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                    excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                    allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                    matchOriginAsFallback?: boolean | undefined;
                    cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                    registration?: "manifest" | "runtime" | undefined;
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "background" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    persistent?: boolean | undefined;
                    type?: "module" | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "options" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    browserStyle?: boolean | undefined;
                    openInTab?: boolean | undefined;
                    chromeStyle?: boolean | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sidepanel" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    openAtInstall?: boolean | undefined;
                    defaultIcon?: string | {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | ({
                type?: "popup" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    mv2Key?: "browser_action" | "page_action" | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    defaultIcon?: {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "content-script" | undefined;
                options?: {
                    world?: "ISOLATED" | "MAIN" | undefined;
                    matches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["matches"]>;
                    runAt?: import("../../../types").PerBrowserOption<Manifest.ContentScript["run_at"]>;
                    matchAboutBlank?: import("../../../types").PerBrowserOption<Manifest.ContentScript["match_about_blank"]>;
                    excludeMatches?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_matches"]>;
                    includeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["include_globs"]>;
                    excludeGlobs?: import("../../../types").PerBrowserOption<Manifest.ContentScript["exclude_globs"]>;
                    allFrames?: import("../../../types").PerBrowserOption<Manifest.ContentScript["all_frames"]>;
                    matchOriginAsFallback?: boolean | undefined;
                    cssInjectionMode?: "manifest" | "manual" | "ui" | undefined;
                    registration?: "manifest" | "runtime" | undefined;
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "background" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    persistent?: boolean | undefined;
                    type?: "module" | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sandbox" | "bookmarks" | "history" | "newtab" | "devtools" | "unlisted-page" | "unlisted-script" | "unlisted-style" | "content-script-style" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "options" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    browserStyle?: boolean | undefined;
                    openInTab?: boolean | undefined;
                    chromeStyle?: boolean | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | {
                type?: "sidepanel" | undefined;
                options?: {
                    include?: (string | undefined)[] | undefined;
                    exclude?: (string | undefined)[] | undefined;
                    defaultTitle?: string | undefined;
                    browserStyle?: boolean | undefined;
                    openAtInstall?: boolean | undefined;
                    defaultIcon?: string | {
                        [x: string]: string | undefined;
                    } | undefined;
                } | undefined;
                name?: string | undefined;
                inputPath?: string | undefined;
                outputDir?: string | undefined;
                skipped?: boolean | undefined;
            } | undefined)[] | undefined;
            chunks?: ({
                type?: "asset" | undefined;
                fileName?: string | undefined;
            } | {
                type?: "chunk" | undefined;
                fileName?: string | undefined;
                moduleIds?: (string | undefined)[] | undefined;
            } | undefined)[] | undefined;
        } | undefined)[] | undefined;
    } | undefined;
    start?: {} | undefined;
    stop?: {} | undefined;
    restart?: {} | undefined;
    transformHtml?: {} | undefined;
    reloadExtension?: {} | undefined;
    reloadPage?: {} | undefined;
    reloadContentScript?: {} | undefined;
    restartBrowser?: {} | undefined;
    ws?: {
        send?: {} | undefined;
        on?: {} | undefined;
    } | undefined;
    watcher?: {
        options?: {
            persistent?: boolean | undefined;
            ignored?: string | {
                exec?: {} | undefined;
                test?: {} | undefined;
                readonly source?: string | undefined;
                readonly global?: boolean | undefined;
                readonly ignoreCase?: boolean | undefined;
                readonly multiline?: boolean | undefined;
                lastIndex?: number | undefined;
                compile?: {} | undefined;
                readonly flags?: string | undefined;
                readonly sticky?: boolean | undefined;
                readonly unicode?: boolean | undefined;
                readonly dotAll?: boolean | undefined;
                [Symbol.match]?: {} | undefined;
                [Symbol.replace]?: {} | undefined;
                [Symbol.search]?: {} | undefined;
                [Symbol.split]?: {} | undefined;
                [Symbol.matchAll]?: {} | undefined;
            } | {} | (string | {
                exec?: {} | undefined;
                test?: {} | undefined;
                readonly source?: string | undefined;
                readonly global?: boolean | undefined;
                readonly ignoreCase?: boolean | undefined;
                readonly multiline?: boolean | undefined;
                lastIndex?: number | undefined;
                compile?: {} | undefined;
                readonly flags?: string | undefined;
                readonly sticky?: boolean | undefined;
                readonly unicode?: boolean | undefined;
                readonly dotAll?: boolean | undefined;
                [Symbol.match]?: {} | undefined;
                [Symbol.replace]?: {} | undefined;
                [Symbol.search]?: {} | undefined;
                [Symbol.split]?: {} | undefined;
                [Symbol.matchAll]?: {} | undefined;
            } | {} | undefined)[] | undefined;
            ignoreInitial?: boolean | undefined;
            followSymlinks?: boolean | undefined;
            cwd?: string | undefined;
            disableGlobbing?: boolean | undefined;
            usePolling?: boolean | undefined;
            useFsEvents?: boolean | undefined;
            alwaysStat?: boolean | undefined;
            depth?: number | undefined;
            interval?: number | undefined;
            binaryInterval?: number | undefined;
            ignorePermissionErrors?: boolean | undefined;
            atomic?: (boolean | number) | undefined;
            awaitWriteFinish?: boolean | {
                stabilityThreshold?: number | undefined;
                pollInterval?: number | undefined;
            } | undefined;
        } | undefined;
        ref?: {} | undefined;
        unref?: {} | undefined;
        add?: {} | undefined;
        unwatch?: {} | undefined;
        getWatched?: {} | undefined;
        close?: {} | undefined;
        on?: {} | undefined;
        [EventEmitter.captureRejectionSymbol]?: {} | undefined;
        addListener?: {} | undefined;
        once?: {} | undefined;
        removeListener?: {} | undefined;
        off?: {} | undefined;
        removeAllListeners?: {} | undefined;
        setMaxListeners?: {} | undefined;
        getMaxListeners?: {} | undefined;
        listeners?: {} | undefined;
        rawListeners?: {} | undefined;
        emit?: {} | undefined;
        listenerCount?: {} | undefined;
        prependListener?: {} | undefined;
        prependOnceListener?: {} | undefined;
        eventNames?: {} | undefined;
    } | undefined;
    on?: {} | undefined;
    port?: number | undefined;
    hostname?: string | undefined;
    origin?: string | undefined;
} | undefined) => WxtDevServer;
export {};
