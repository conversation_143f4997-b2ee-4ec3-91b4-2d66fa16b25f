import type { Manifest } from 'wxt/browser';
import { BuildOutput, Entrypoint, EntrypointGroup } from '../../../types';
/**
 * Given a configuration, list of entrypoints, and an existing, partial output, build the
 * entrypoints and merge the new output with the existing output.
 *
 * This function will:
 * 1. Generate the .wxt directory's types
 * 2. Build the `entrypointGroups` (and copies public files)
 * 3. Generate the latest manifest for all entrypoints
 * 4. Write the new manifest to the file system
 *
 * @param config Internal config containing all the project information.
 * @param allEntrypoints List of entrypoints used to generate the types inside .wxt directory.
 * @param entrypointGroups The list of entrypoint groups to build.
 * @param existingOutput The previous output to combine the rebuild results into. An emptry array if
 *                       this is the first build.
 */
export declare function rebuild(allEntrypoints: Entrypoint[], entrypointGroups: EntrypointGroup[], existingOutput?: Omit<BuildOutput, 'manifest'>): Promise<{
    output: BuildOutput;
    manifest: Manifest.WebExtensionManifest;
    warnings: any[][];
}>;
