/**
 * Utilities for creating reusable, build-time modules for WXT.
 *
 * @module wxt/modules
 */
import type { Entrypoint, Wxt, WxtModule, WxtModuleOptions, WxtModuleSetup } from './types';
import * as vite from 'vite';
import type { UnimportOptions } from 'unimport';
export { WxtModule };
export declare function defineWxtModule<TOptions extends WxtModuleOptions>(module: WxtModule<TOptions> | WxtModuleSetup<TOptions>): WxtModule<TOptions>;
/**
 * Adds a TS/JS file as an entrypoint to the project. This file will be bundled
 * along with the other entrypoints.
 *
 * If you're publishing the module to NPM, you should probably pre-build the
 * entrypoint and use `addPublicAssets` instead to copy pre-bundled assets into
 * the output directory. This will speed up project builds since it just has to
 * copy some files instead of bundling them.
 *
 * To extract entrypoint options from a JS/TS file, use
 * `wxt.builder.importEntrypoint` (see example).
 *
 * @param wxt The wxt instance provided by the module's setup function.
 * @param entrypoint The entrypoint to be bundled along with the extension.
 *
 * @example
 * export default defineWxtModule(async (wxt, options) => {
 *   const entrypointPath = "/path/to/my-entrypoint.ts";
 *   addEntrypoint(wxt, {
 *     type: "content-script",
 *     name: "some-name",
 *     inputPath: entrypointPath,
 *     outputDir: wxt.config.outDir,
 *     options: await wxt.builder.importEntrypoint(entrypointPath),
 *   });
 * });
 */
export declare function addEntrypoint(wxt: Wxt, entrypoint: Entrypoint): void;
/**
 * Copy files inside a directory (as if it were the public directory) into the
 * extension's output directory. The directory itself is not copied, just the
 * files inside it. If a filename matches an existing one, it is ignored.
 *
 * @param wxt The wxt instance provided by the module's setup function.
 * @param dir The directory to copy.
 *
 * @example
 * export default defineWxtModule((wxt, options) => {
 *   addPublicAssets(wxt, "./dist/prebundled");
 * });
 */
export declare function addPublicAssets(wxt: Wxt, dir: string): void;
/**
 * Merge additional vite config for one or more entrypoint "groups" that make
 * up individual builds. Config in the project's `wxt.config.ts` file takes
 * precedence over any config added by this function.
 *
 * @param wxt The wxt instance provided by the module's setup function.
 * @param viteConfig A function that returns the vite config the module is
                     adding. Same format as `vite` in `wxt.config.ts`.
 *
 * @example
 * export default defineWxtModule((wxt, options) => {
 *   addViteConfig(wxt, () => ({
 *     build: {
 *       sourceMaps: true,
 *     },
 *   });
 * });
 */
export declare function addViteConfig(wxt: Wxt, viteConfig: (env: vite.ConfigEnv) => vite.UserConfig | undefined): void;
/**
 * Add a runtime plugin to the project. In each entrypoint, before executing
 * the `main` function, plugins are executed.
 *
 * @param wxt The wxt instance provided by the module's setup function.
 * @param plugin An import from an NPM module, or an absolute file path to the
 *               file to load at runtime.
 *
 * @example
 * export default defineWxtModule((wxt) => {
 *   addWxtPlugin(wxt, "wxt-module-analytics/client-plugin");
 * });
 */
export declare function addWxtPlugin(wxt: Wxt, plugin: string): void;
/**
 * Add an Unimport preset ([built-in](https://github.com/unjs/unimport?tab=readme-ov-file#built-in-presets),
 * [custom](https://github.com/unjs/unimport?tab=readme-ov-file#custom-presets),
 * or [auto-scanned](https://github.com/unjs/unimport?tab=readme-ov-file#exports-auto-scan)),
 * to the project's list of auto-imported utilities.
 *
 * Some things to note:
 * - This function will only de-duplicate built-in preset names. It will not
 *   stop you adding duplicate custom or auto-scanned presets.
 * - If the project has disabled imports, this function has no effect.
 *
 * @param wxt The wxt instance provided by the module's setup function.
 * @param preset The preset to add to the project.
 *
 * @example
 * export default defineWxtModule((wxt) => {
 *   // Built-in preset:
 *   addImportPreset(wxt, "vue");
 *   // Custom preset:
 *   addImportPreset(wxt, {
 *     from: "vue",
 *     imports: ["ref", "reactive", ...],
 *   });
 *   // Auto-scanned preset:
 *   addImportPreset(wxt, { package: "vue" });
 * });
 */
export declare function addImportPreset(wxt: Wxt, preset: UnimportOptions['presets'][0]): void;
/**
 * Adds an import alias to the project's TSConfig paths and bundler. Path can
 * be absolute or relative to the project's root directory.
 *
 * Usually, this is used to provide access to some code generated by your
 * module. In the example below, a `i18n` plugin generates a variable that it
 * wants to provide access to, so it creates the file and adds an import alias
 * to it.
 *
 * @example
 * import path from 'node:path';
 *
 * export default defineWxtModule((wxt) => {
 *   const i18nPath = path.resolve(wxt.config.wxtDir, "i18n.ts");
 *
 *   // Generate the file
 *   wxt.hooks.hook("prepare:types", (_, entries) => {
 *     entries.push({
 *       path: i18nPath,
 *       text: `export const i18n = ...`,
 *     });
 *   });
 *
 *   // Add alias
 *   addAlias(wxt, "#i18n", i18nPath);
 * });
 */
export declare function addAlias(wxt: Wxt, alias: string, path: string): void;
