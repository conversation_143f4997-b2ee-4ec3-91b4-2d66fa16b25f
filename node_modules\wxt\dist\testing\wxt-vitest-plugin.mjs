import {
  download,
  tsconfigPaths,
  globals,
  extensionApiMock,
  resolveAppConfig
} from "../core/builders/vite/plugins/index.mjs";
import UnimportPlugin from "unimport/unplugin";
import { registerWxt, wxt } from "../core/wxt.mjs";
export async function WxtVitest(inlineConfig) {
  await registerWxt("serve", inlineConfig ?? {});
  const plugins = [
    globals(wxt.config),
    download(wxt.config),
    tsconfigPaths(wxt.config),
    resolveAppConfig(wxt.config),
    extensionApiMock(wxt.config)
  ];
  if (wxt.config.imports !== false) {
    plugins.push(UnimportPlugin.vite(wxt.config.imports));
  }
  return plugins;
}
