import { getPublicFiles } from "../../utils/fs.mjs";
import fs from "fs-extra";
import { dirname, resolve } from "path";
import pc from "picocolors";
import { wxt } from "../../wxt.mjs";
import { toArray } from "../arrays.mjs";
export async function buildEntrypoints(groups, spinner) {
  const steps = [];
  for (let i = 0; i < groups.length; i++) {
    const group = groups[i];
    const groupNames = toArray(group).map((e) => e.name);
    const groupNameColored = groupNames.join(pc.dim(", "));
    spinner.text = pc.dim(`[${i + 1}/${groups.length}]`) + ` ${groupNameColored}`;
    try {
      steps.push(await wxt.builder.build(group));
    } catch (err) {
      spinner.stop().clear();
      wxt.logger.error(err);
      throw Error(`Failed to build ${groupNames.join(", ")}`, { cause: err });
    }
  }
  const publicAssets = await copyPublicDirectory();
  return { publicAssets, steps };
}
async function copyPublicDirectory() {
  const files = (await getPublicFiles()).map((file) => ({
    absoluteSrc: resolve(wxt.config.publicDir, file),
    relativeDest: file
  }));
  await wxt.hooks.callHook("build:publicAssets", wxt, files);
  if (files.length === 0) return [];
  const publicAssets = [];
  for (const file of files) {
    const absoluteDest = resolve(wxt.config.outDir, file.relativeDest);
    await fs.ensureDir(dirname(absoluteDest));
    if ("absoluteSrc" in file) {
      await fs.copyFile(file.absoluteSrc, absoluteDest);
    } else {
      await fs.writeFile(absoluteDest, file.contents, "utf8");
    }
    publicAssets.push({
      type: "asset",
      fileName: file.relativeDest
    });
  }
  return publicAssets;
}
