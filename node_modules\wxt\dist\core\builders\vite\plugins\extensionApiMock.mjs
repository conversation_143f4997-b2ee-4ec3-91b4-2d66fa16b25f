import path from "node:path";
export function extensionApiMock(config) {
  const virtualSetupModule = "virtual:wxt-setup";
  const resolvedVirtualSetupModule = "\0" + virtualSetupModule;
  return {
    name: "wxt:extension-api-mock",
    config() {
      const replacement = path.resolve(
        config.wxtModuleDir,
        "dist/virtual/mock-browser"
      );
      return {
        test: {
          setupFiles: [virtualSetupModule]
        },
        resolve: {
          alias: [
            { find: "webextension-polyfill", replacement },
            // wxt/browser, wxt/browser/...
            { find: /^wxt\/browser.*/, replacement }
          ]
        },
        ssr: {
          // Inline all WXT modules so vite processes them so the aliases can
          // be resolved
          noExternal: ["wxt"]
        }
      };
    },
    resolveId(id) {
      if (id.endsWith(virtualSetupModule)) return resolvedVirtualSetupModule;
    },
    load(id) {
      if (id === resolvedVirtualSetupModule) return setupTemplate;
    }
  };
}
const setupTemplate = `
  import { vi } from 'vitest';
  import { fakeBrowser } from 'wxt/testing';

  vi.stubGlobal("chrome", fakeBrowser);
  vi.stubGlobal("browser", fakeBrowser);
`;
