import type { Manifest } from 'wxt/browser';
import { Entrypoint, BuildOutput, ContentScriptEntrypoint } from '../../types';
/**
 * Writes the manifest to the output directory and the build output.
 */
export declare function writeManifest(manifest: Manifest.WebExtensionManifest, output: BuildOutput): Promise<void>;
/**
 * Generates the manifest based on the config and entrypoints.
 */
export declare function generateManifest(allEntrypoints: Entrypoint[], buildOutput: Omit<BuildOutput, 'manifest'>): Promise<{
    manifest: Manifest.WebExtensionManifest;
    warnings: any[][];
}>;
/**
 * Returns the bundle paths to CSS files associated with a list of content scripts, or undefined if
 * there is no associated CSS.
 */
export declare function getContentScriptCssFiles(contentScripts: ContentScriptEntrypoint[], contentScriptCssMap: Record<string, string | undefined>): string[] | undefined;
/**
 * Content scripts configured with `cssInjectionMode: "ui"` need to add their CSS files to web
 * accessible resources so they can be fetched as text and added to shadow roots that the UI is
 * added to.
 */
export declare function getContentScriptCssWebAccessibleResources(contentScripts: ContentScriptEntrypoint[], contentScriptCssMap: Record<string, string | undefined>): any[];
/**
 * Based on the build output, return a Record of each content script's name to it CSS file if the
 * script includes one.
 */
export declare function getContentScriptsCssMap(buildOutput: Omit<BuildOutput, 'manifest'>, scripts: ContentScriptEntrypoint[]): Record<string, string | undefined>;
/**
 * - "<all_urls>" &rarr; "<all_urls>"
 * - "*://play.google.com/books/*" &rarr; "*://play.google.com/*"
 */
export declare function stripPathFromMatchPattern(pattern: string): string;
/**
 * Converts all MV3 web accessible resources to their MV2 forms. MV3 web accessible resources are
 * generated in this file, and may be defined by the user in their manifest. In both cases, when
 * targeting MV2, automatically convert their definitions down to the basic MV2 array.
 */
export declare function convertWebAccessibleResourcesToMv2(manifest: Manifest.WebExtensionManifest): void;
/**
 * Make sure all resources are in MV3 format. If not, add a wanring
 */
export declare function validateMv3WebAccessibleResources(manifest: Manifest.WebExtensionManifest): void;
