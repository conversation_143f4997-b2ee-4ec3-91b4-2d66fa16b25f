#!/bin/bash

# Clean build script for YouTube Focus Extension
# This creates a production build without development dependencies

echo "🧹 Cleaning previous builds..."
rm -rf dist/chrome-mv3
rm -rf .output

echo "🔨 Building with WXT..."
npm run build

echo "📁 Creating clean distribution..."
mkdir -p dist/chrome-mv3

echo "📄 Creating clean manifest..."
cat > dist/chrome-mv3/manifest.json << 'EOF'
{
  "manifest_version": 3,
  "name": "YouTube Focus Extension",
  "description": "A focused YouTube experience without distracting recommendations",
  "version": "1.0.0",
  "permissions": [
    "activeTab",
    "storage"
  ],
  "host_permissions": [
    "*://www.youtube.com/*",
    "*://youtube.com/*"
  ],
  "action": {
    "default_title": "YouTube Focus Extension",
    "default_popup": "popup.html"
  },
  "content_scripts": [
    {
      "matches": ["*://www.youtube.com/*", "*://youtube.com/*"],
      "css": ["content-scripts/youtube.css"],
      "js": ["content-scripts/youtube.js"]
    }
  ]
}
EOF

echo "📋 Copying necessary files..."
cp .output/chrome-mv3/popup.html dist/chrome-mv3/
cp -r .output/chrome-mv3/content-scripts dist/chrome-mv3/
cp -r .output/chrome-mv3/chunks dist/chrome-mv3/

echo "✅ Clean build completed!"
echo "📦 Extension ready at: dist/chrome-mv3/"
echo ""
echo "To install:"
echo "1. Open Chrome and go to chrome://extensions/"
echo "2. Enable 'Developer mode'"
echo "3. Click 'Load unpacked'"
echo "4. Select the 'dist/chrome-mv3' folder"
