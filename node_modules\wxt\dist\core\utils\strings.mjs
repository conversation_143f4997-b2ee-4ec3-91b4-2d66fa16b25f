import { camelCase } from "scule";
export function kebabCaseAlphanumeric(str) {
  return str.toLowerCase().replace(/[^a-z0-9-\s]/g, "").replace(/\s+/g, "-");
}
export function safeVarName(str) {
  const name = camelCase(kebabCaseAlphanumeric(str));
  if (name.match(/^[a-z]/)) return name;
  return "_" + name;
}
export function safeFilename(str) {
  return kebabCaseAlphanumeric(str);
}
export function removeImportStatements(text) {
  return text.replace(
    /(import\s?[\s\S]*?from\s?["'][\s\S]*?["'];?|import\s?["'][\s\S]*?["'];?)/gm,
    ""
  );
}
export function removeProjectImportStatements(text) {
  const noImports = removeImportStatements(text);
  return `import { defineUnlistedScript, defineContentScript, defineBackground } from 'wxt/sandbox';

${noImports}`;
}
