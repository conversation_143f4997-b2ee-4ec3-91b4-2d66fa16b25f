{"manifest_version": 3, "name": "YouTube Focus Extension", "description": "A focused YouTube experience without distracting recommendations", "version": "1.0.0", "permissions": ["activeTab", "storage", "tabs", "scripting"], "host_permissions": ["*://www.youtube.com/*", "*://youtube.com/*", "http://localhost/*"], "commands": {"wxt:reload-extension": {"description": "Reload the extension during development", "suggested_key": {"default": "Alt+R"}}}, "background": {"service_worker": "background.js"}, "action": {"default_title": "YouTube Focus Extension", "default_popup": "popup.html"}, "content_security_policy": {"extension_pages": "script-src 'self' 'wasm-unsafe-eval' http://localhost:3000; object-src 'self';", "sandbox": "script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:3000; sandbox allow-scripts allow-forms allow-popups allow-modals; child-src 'self';"}}