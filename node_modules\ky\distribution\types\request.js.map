{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../source/types/request.ts"], "names": [], "mappings": "AAAA;;;;;EAKE", "sourcesContent": ["/*\nUndici types need to be here because they are not exported to globals by @types/node.\nSee https://github.com/DefinitelyTyped/DefinitelyTyped/discussions/69408\n\nAfter the types are exported to globals, the Undici types can be removed from here.\n*/\n\ntype UndiciHeadersInit =\n\t| string[][]\n\t| Record<string, string | readonly string[]>\n\t| Headers;\n\ntype UndiciBodyInit =\n\t| ArrayBuffer\n\t| AsyncIterable<Uint8Array>\n\t| Blob\n\t| FormData\n\t| Iterable<Uint8Array>\n\t| ArrayBufferView\n\t| URLSearchParams\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\t| null\n\t| string;\n\ntype UndiciRequestRedirect = 'error' | 'follow' | 'manual';\n\ntype UndiciRequestCredentials = 'omit' | 'include' | 'same-origin';\n\ntype UndiciReferrerPolicy =\n\t| ''\n\t| 'no-referrer'\n\t| 'no-referrer-when-downgrade'\n\t| 'origin'\n\t| 'origin-when-cross-origin'\n\t| 'same-origin'\n\t| 'strict-origin'\n\t| 'strict-origin-when-cross-origin'\n\t| 'unsafe-url';\n\ntype UndiciRequestMode = 'cors' | 'navigate' | 'no-cors' | 'same-origin';\n\ntype UndiciRequestInit = {\n\tmethod?: string;\n\tkeepalive?: boolean;\n\theaders?: UndiciHeadersInit;\n\tbody?: UndiciBodyInit;\n\tredirect?: UndiciRequestRedirect;\n\tintegrity?: string;\n\tsignal?: AbortSignal | undefined;\n\tcredentials?: UndiciRequestCredentials;\n\tmode?: UndiciRequestMode;\n\treferrer?: string;\n\treferrerPolicy?: UndiciReferrerPolicy;\n\twindow?: undefined;\n\tdispatcher?: unknown;\n\tduplex?: unknown;\n};\n\ntype CombinedRequestInit = globalThis.RequestInit & UndiciRequestInit;\n\nexport type RequestInitRegistry = {[K in keyof CombinedRequestInit]-?: true};\n\nexport type KyRequest<T = unknown> = {\n\tjson: <J = T>() => Promise<J>;\n} & Request;\n"]}