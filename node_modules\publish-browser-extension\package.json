{"name": "publish-browser-extension", "description": "Easily publish web extensions to their stores", "version": "3.0.0", "engines": {"node": "^18.0.0 || >=20.0.0"}, "license": "MIT", "keywords": ["chrome-web-store", "firefox-addon-store", "chrome", "firefox", "addon", "store", "extension", "browser"], "author": {"name": "<PERSON>", "url": "https://github.com/aklinker1"}, "repository": {"type": "git", "url": "https://github.com/aklinker1/publish-browser-extension"}, "homepage": "https://github.com/aklinker1/publish-browser-extension#readme", "files": ["dist"], "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "bin": {"publish-extension": "bin/publish-extension.cjs"}, "exports": {".": {"require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}, "import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "./cli": {"require": "./dist/cli.cjs", "import": "./dist/cli.js"}}, "dependencies": {"cac": "^6.7.14", "cli-highlight": "^2.1.11", "consola": "^3.2.3", "dotenv": "^16.3.1", "extract-zip": "^2.0.1", "formdata-node": "^6.0.3", "listr2": "^8.0.1", "lodash.camelcase": "^4.3.0", "lodash.kebabcase": "^4.1.1", "lodash.snakecase": "^4.1.1", "ofetch": "^1.3.3", "open": "^9.1.0", "ora": "^6.3.1", "prompts": "^2.4.2", "zod": "^3.22.4"}, "devDependencies": {"@aklinker1/check": "^1.3.1", "@types/jsonwebtoken": "^8.5.8", "@types/lodash.camelcase": "^4.3.7", "@types/lodash.kebabcase": "^4.1.7", "@types/lodash.snakecase": "^4.1.7", "@types/node": "^18", "@types/node-fetch": "^2", "@types/prompts": "^2.4.4", "@vitest/coverage-v8": "^1.2.0", "archiver": "^5.3.1", "jsonwebtoken": "^8.5.1", "lint-staged": "^15.2.0", "npm-run-all": "^4.1.5", "prettier": "^3.2.2", "publint": "^0.2.7", "rimraf": "^4.4.0", "run-all": "^1.0.1", "simple-git-hooks": "^2.9.0", "tsup": "^8.0.1", "tsx": "^3.12.5", "typescript": "^5.3.3", "vitest": "^1.2.0"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "prettier --ignore-unknown --write"}, "changelog": {"excludeAuthors": ["<EMAIL>"]}, "scripts": {"check": "check", "build": "run-s build:*", "build:clean": "<PERSON><PERSON><PERSON> dist", "build:lib": "tsup src/index.ts --dts --format cjs,esm --sourcemap", "build:cli": "tsup --format cjs,esm --entry.cli src/cli.ts", "build:test-extension": "node scripts/build-test-extension.js", "test": "vitest", "test:coverage": "vitest --coverage", "dev:all": "./scripts/dev.sh all", "dev:chrome": "./scripts/dev.sh chrome", "dev:firefox": "./scripts/dev.sh firefox", "dev:edge": "./scripts/dev.sh edge", "dev:help": "./scripts/dev.sh help", "publish-extension": "tsx src/cli.ts"}}