import type * as vite from 'vite';
import { Entrypoint, ResolvedConfig } from '../../../../types';
/**
 * Ensures the HTML files output by a multipage build are in the correct location. This does two
 * things:
 *
 * 1. Moves the HMTL files to their final location at `<outDir>/<entrypoint.name>.html`.
 * 2. Updates the bundle so it summarizes the files correctly in the returned build output.
 *
 * Assets (JS and CSS) are output to the `<outDir>/assets` directory, and don't need to be modified.
 * HTML files access them via absolute URLs, so we don't need to update any import paths in the HTML
 * files either.
 *
 * THIS PLUGIN SHOULD ONLY BE APPLIED TO MULTIPAGE BUILDS. It should not be added to every build.
 */
export declare function multipageMove(entrypoints: Entrypoint[], config: ResolvedConfig): vite.Plugin;
/**
 * Recursively remove all directories that are empty/
 */
export declare function removeEmptyDirs(dir: string): Promise<void>;
