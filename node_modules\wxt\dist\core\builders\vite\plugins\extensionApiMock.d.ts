import type * as vite from 'vite';
import { ResolvedConfig } from '../../../../types';
/**
 * Mock `webextension-polyfill`, `wxt/browser`, and `wxt/browser/*` by inlining
 * all dependencies that import them and adding a custom alias so that Vite
 * resolves to a mocked version of the module.
 *
 * TODO: Detect non-wxt dependencies (like `@webext-core/*`) that import `webextension-polyfill` via
 * `npm list` and inline them automatically.
 */
export declare function extensionApiMock(config: ResolvedConfig): vite.PluginOption;
