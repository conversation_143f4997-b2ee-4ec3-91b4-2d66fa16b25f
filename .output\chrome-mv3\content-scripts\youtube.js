var youtube=function(){"use strict";var K=Object.defineProperty;var _=(u,a,l)=>a in u?K(u,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):u[a]=l;var r=(u,a,l)=>_(u,typeof a!="symbol"?a+"":a,l);var g,v;function u(i){return i}const a={matches:["*://www.youtube.com/*","*://youtube.com/*"],main(){console.log("YouTube Focus Extension loaded");class i{constructor(){r(this,"modal",null);r(this,"isHomepage",!1);r(this,"isVideoPage",!1);this.init()}init(){document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>this.handlePageLoad()):this.handlePageLoad(),this.observeUrlChanges()}handlePageLoad(){this.detectPageType(),this.isHomepage?this.handleHomepage():this.isVideoPage&&this.handleVideoPage()}detectPageType(){const e=window.location.href,s=window.location.pathname;this.isHomepage=s==="/"||s===""||e.includes("youtube.com/?"),this.isVideoPage=s.includes("/watch")&&e.includes("v=")}observeUrlChanges(){let e=location.href;new MutationObserver(()=>{const s=location.href;s!==e&&(e=s,setTimeout(()=>this.handlePageLoad(),500))}).observe(document,{subtree:!0,childList:!0})}handleHomepage(){console.log("Handling homepage"),this.showSearchModal()}handleVideoPage(){console.log("Handling video page"),this.hideRecommendations()}showSearchModal(){this.modal&&this.modal.remove(),this.modal=document.createElement("div"),this.modal.className="youtube-focus-modal",this.modal.innerHTML=`
          <div class="youtube-focus-modal-content">
            <div class="youtube-focus-modal-header">
              <h2>Search YouTube Videos</h2>
              <button class="youtube-focus-close-btn">&times;</button>
            </div>
            <div class="youtube-focus-modal-body">
              <div class="youtube-focus-search-container">
                <input type="text" id="youtube-focus-search" placeholder="Search for videos..." />
                <button id="youtube-focus-search-btn">Search</button>
              </div>
              <div id="youtube-focus-results" class="youtube-focus-results"></div>
            </div>
          </div>
        `,document.body.appendChild(this.modal),this.setupModalEventListeners()}setupModalEventListeners(){if(!this.modal)return;const e=this.modal.querySelector(".youtube-focus-close-btn"),s=this.modal.querySelector("#youtube-focus-search-btn"),o=this.modal.querySelector("#youtube-focus-search");e==null||e.addEventListener("click",()=>this.closeModal()),s==null||s.addEventListener("click",()=>this.performSearch()),o==null||o.addEventListener("keypress",n=>{n.key==="Enter"&&this.performSearch()}),this.modal.addEventListener("click",n=>{n.target===this.modal&&this.closeModal()})}closeModal(){this.modal&&(this.modal.remove(),this.modal=null)}async performSearch(){const e=document.querySelector("#youtube-focus-search"),s=document.querySelector("#youtube-focus-results");if(!e||!s)return;const o=e.value.trim();if(o){s.innerHTML='<div class="youtube-focus-loading">Searching...</div>';try{const n=`https://www.youtube.com/results?search_query=${encodeURIComponent(o)}`;window.location.href=n}catch{s.innerHTML='<div class="youtube-focus-error">Search failed. Please try again.</div>'}}}hideRecommendations(){["#secondary","#related",".ytd-watch-next-secondary-results-renderer",".ytp-endscreen-content",".ytp-ce-element","#comments",".ytd-compact-video-renderer",".ytd-video-secondary-info-renderer #meta-contents #container"].forEach(o=>{document.querySelectorAll(o).forEach(c=>{c.style.display="none"})});const s=document.querySelector(".ytp-autonav-toggle-button");s&&(s.style.display="none"),this.observeRecommendations()}observeRecommendations(){new MutationObserver(()=>{this.hideRecommendations()}).observe(document.body,{childList:!0,subtree:!0})}}new i}},l=((v=(g=globalThis.browser)==null?void 0:g.runtime)==null?void 0:v.id)==null?globalThis.chrome:globalThis.browser;function h(i,...t){}const S={debug:(...i)=>h(console.debug,...i),log:(...i)=>h(console.log,...i),warn:(...i)=>h(console.warn,...i),error:(...i)=>h(console.error,...i)},y=class y extends Event{constructor(t,e){super(y.EVENT_NAME,{}),this.newUrl=t,this.oldUrl=e}};r(y,"EVENT_NAME",f("wxt:locationchange"));let b=y;function f(i){var t;return`${(t=l==null?void 0:l.runtime)==null?void 0:t.id}:youtube:${i}`}function E(i){let t,e;return{run(){t==null&&(e=new URL(location.href),t=i.setInterval(()=>{let s=new URL(location.href);s.href!==e.href&&(window.dispatchEvent(new b(s,e)),e=s)},1e3))}}}const d=class d{constructor(t,e){r(this,"isTopFrame",window.self===window.top);r(this,"abortController");r(this,"locationWatcher",E(this));r(this,"receivedMessageIds",new Set);this.contentScriptName=t,this.options=e,this.abortController=new AbortController,this.isTopFrame?(this.listenForNewerScripts({ignoreFirstEvent:!0}),this.stopOldScripts()):this.listenForNewerScripts()}get signal(){return this.abortController.signal}abort(t){return this.abortController.abort(t)}get isInvalid(){return l.runtime.id==null&&this.notifyInvalidated(),this.signal.aborted}get isValid(){return!this.isInvalid}onInvalidated(t){return this.signal.addEventListener("abort",t),()=>this.signal.removeEventListener("abort",t)}block(){return new Promise(()=>{})}setInterval(t,e){const s=setInterval(()=>{this.isValid&&t()},e);return this.onInvalidated(()=>clearInterval(s)),s}setTimeout(t,e){const s=setTimeout(()=>{this.isValid&&t()},e);return this.onInvalidated(()=>clearTimeout(s)),s}requestAnimationFrame(t){const e=requestAnimationFrame((...s)=>{this.isValid&&t(...s)});return this.onInvalidated(()=>cancelAnimationFrame(e)),e}requestIdleCallback(t,e){const s=requestIdleCallback((...o)=>{this.signal.aborted||t(...o)},e);return this.onInvalidated(()=>cancelIdleCallback(s)),s}addEventListener(t,e,s,o){var n;e==="wxt:locationchange"&&this.isValid&&this.locationWatcher.run(),(n=t.addEventListener)==null||n.call(t,e.startsWith("wxt:")?f(e):e,s,{...o,signal:this.signal})}notifyInvalidated(){this.abort("Content script context invalidated"),S.debug(`Content script "${this.contentScriptName}" context invalidated`)}stopOldScripts(){window.postMessage({type:d.SCRIPT_STARTED_MESSAGE_TYPE,contentScriptName:this.contentScriptName,messageId:Math.random().toString(36).slice(2)},"*")}verifyScriptStartedEvent(t){var n,c,w;const e=((n=t.data)==null?void 0:n.type)===d.SCRIPT_STARTED_MESSAGE_TYPE,s=((c=t.data)==null?void 0:c.contentScriptName)===this.contentScriptName,o=!this.receivedMessageIds.has((w=t.data)==null?void 0:w.messageId);return e&&s&&o}listenForNewerScripts(t){let e=!0;const s=o=>{if(this.verifyScriptStartedEvent(o)){this.receivedMessageIds.add(o.data.messageId);const n=e;if(e=!1,n&&(t!=null&&t.ignoreFirstEvent))return;this.notifyInvalidated()}};addEventListener("message",s),this.onInvalidated(()=>removeEventListener("message",s))}};r(d,"SCRIPT_STARTED_MESSAGE_TYPE",f("wxt:content-script-started"));let p=d;const T=Symbol("null");let M=0;class I extends Map{constructor(){super(),this._objectHashes=new WeakMap,this._symbolHashes=new Map,this._publicKeys=new Map;const[t]=arguments;if(t!=null){if(typeof t[Symbol.iterator]!="function")throw new TypeError(typeof t+" is not iterable (cannot read property Symbol(Symbol.iterator))");for(const[e,s]of t)this.set(e,s)}}_getPublicKeys(t,e=!1){if(!Array.isArray(t))throw new TypeError("The keys parameter must be an array");const s=this._getPrivateKey(t,e);let o;return s&&this._publicKeys.has(s)?o=this._publicKeys.get(s):e&&(o=[...t],this._publicKeys.set(s,o)),{privateKey:s,publicKey:o}}_getPrivateKey(t,e=!1){const s=[];for(let o of t){o===null&&(o=T);const n=typeof o=="object"||typeof o=="function"?"_objectHashes":typeof o=="symbol"?"_symbolHashes":!1;if(!n)s.push(o);else if(this[n].has(o))s.push(this[n].get(o));else if(e){const c=`@@mkm-ref-${M++}@@`;this[n].set(o,c),s.push(c)}else return!1}return JSON.stringify(s)}set(t,e){const{publicKey:s}=this._getPublicKeys(t,!0);return super.set(s,e)}get(t){const{publicKey:e}=this._getPublicKeys(t);return super.get(e)}has(t){const{publicKey:e}=this._getPublicKeys(t);return super.has(e)}delete(t){const{publicKey:e,privateKey:s}=this._getPublicKeys(t);return!!(e&&super.delete(e)&&this._publicKeys.delete(s))}clear(){super.clear(),this._symbolHashes.clear(),this._publicKeys.clear()}get[Symbol.toStringTag](){return"ManyKeysMap"}get size(){return super.size}}new I;function L(){}function m(i,...t){}const P={debug:(...i)=>m(console.debug,...i),log:(...i)=>m(console.log,...i),warn:(...i)=>m(console.warn,...i),error:(...i)=>m(console.error,...i)};return(async()=>{try{const{main:i,...t}=a,e=new p("youtube",t);return await i(e)}catch(i){throw P.error('The content script "youtube" crashed on startup!',i),i}})()}();
youtube;
