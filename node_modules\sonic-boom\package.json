{"name": "sonic-boom", "version": "4.2.0", "description": "Extremely fast utf8 only stream implementation", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "scripts": {"test": "npm run test:types && standard && npm run test:unit", "test:unit": "tap", "test:types": "tsc && tsd"}, "pre-commit": ["test"], "repository": {"type": "git", "url": "git+https://github.com/pinojs/sonic-boom.git"}, "keywords": ["stream", "fs", "net", "fd", "file", "descriptor", "fast"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/pinojs/sonic-boom/issues"}, "homepage": "https://github.com/pinojs/sonic-boom#readme", "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@sinonjs/fake-timers": "^13.0.1", "@types/node": "^22.0.0", "fastbench": "^1.0.1", "proxyquire": "^2.1.3", "standard": "^17.0.0", "tap": "^18.2.0", "tsd": "^0.31.0", "typescript": "^5.0.2"}, "dependencies": {"atomic-sleep": "^1.0.0"}, "tsd": {"directory": "./types"}}