import { addViteConfig, defineWxtModule } from "../modules.mjs";
import { createUnimport } from "unimport";
import UnimportPlugin from "unimport/unplugin";
export default defineWxtModule({
  name: "wxt:built-in:unimport",
  setup(wxt) {
    const options = wxt.config.imports;
    if (options === false) return;
    let unimport;
    wxt.hooks.hook("config:resolved", () => {
      const addModuleImports = (module) => {
        if (!module.imports) return;
        options.imports ??= [];
        options.imports.push(...module.imports);
      };
      wxt.config.builtinModules.forEach(addModuleImports);
      wxt.config.userModules.forEach(addModuleImports);
    });
    wxt.hooks.afterEach((event) => {
      if (event.name === "config:resolved") {
        unimport = createUnimport(options);
      }
    });
    wxt.hooks.hook("prepare:types", async (_, entries) => {
      await unimport.init();
      entries.push(await getImportsDeclarationEntry(unimport));
      if (options.eslintrc.enabled === false) return;
      entries.push(
        await getEslintConfigEntry(unimport, options.eslintrc.enabled, options)
      );
    });
    addViteConfig(wxt, () => ({
      plugins: [UnimportPlugin.vite(options)]
    }));
  }
});
async function getImportsDeclarationEntry(unimport) {
  await unimport.init();
  return {
    path: "types/imports.d.ts",
    text: [
      "// Generated by wxt",
      await unimport.generateTypeDeclarations(),
      ""
    ].join("\n"),
    tsReference: true
  };
}
async function getEslintConfigEntry(unimport, version, options) {
  const globals = (await unimport.getImports()).map((i) => i.as ?? i.name).filter(Boolean).sort().reduce((globals2, name) => {
    globals2[name] = options.eslintrc.globalsPropValue;
    return globals2;
  }, {});
  if (version <= 8) return getEslint8ConfigEntry(options, globals);
  else return getEslint9ConfigEntry(options, globals);
}
export function getEslint8ConfigEntry(options, globals) {
  return {
    path: options.eslintrc.filePath,
    text: JSON.stringify({ globals }, null, 2) + "\n"
  };
}
export function getEslint9ConfigEntry(options, globals) {
  return {
    path: options.eslintrc.filePath,
    text: `const globals = ${JSON.stringify(globals, null, 2)}

export default {
  name: "wxt/auto-imports",
  languageOptions: {
    globals,
    sourceType: "module",
  },
};
`
  };
}
