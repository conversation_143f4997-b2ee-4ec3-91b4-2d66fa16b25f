export type ScriptPublicPath = Extract<import('wxt/browser').PublicPath, `${string}.js`>;
/**
 * This function can only be called inside content scripts.
 *
 * Inject an unlisted script into the page. Scripts are added to the `<head>`
 * element or `document.documentElement` if there is no head.
 *
 * Make sure to add the injected script to your manifest's
 * `web_accessible_resources`.
 */
export declare function injectScript(path: ScriptPublicPath, options?: InjectScriptOptions): Promise<void>;
export interface InjectScriptOptions {
    /**
     * By default, the injected script is removed from the DOM after being
     * injected. To disable this behavior, set this flag to true.
     */
    keepInDom?: boolean;
}
