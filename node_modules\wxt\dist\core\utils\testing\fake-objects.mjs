import { resolve } from "path";
import { faker } from "@faker-js/faker";
import merge from "lodash.merge";
import { mock } from "vitest-mock-extended";
import { vi } from "vitest";
import { setWxtForTesting } from "../../../core/wxt.mjs";
faker.seed(import.meta.test.SEED);
function fakeObjectCreator(base) {
  return (overrides) => merge(base(), overrides);
}
export function fakeFileName() {
  return faker.string.alphanumeric() + "." + faker.string.alpha({ length: 3 });
}
export function fakeFile(root = process.cwd()) {
  return resolve(root, fakeFileName());
}
export function fakeDir(root = process.cwd()) {
  return resolve(root, faker.string.alphanumeric());
}
export const fakeEntrypoint = (options) => faker.helpers.arrayElement([
  fakePopupEntrypoint,
  fakeGenericEntrypoint,
  fakeOptionsEntrypoint,
  fakeBackgroundEntrypoint,
  fakeContentScriptEntrypoint,
  fakeUnlistedScriptEntrypoint
])(options);
export const fakeContentScriptEntrypoint = fakeObjectCreator(() => ({
  type: "content-script",
  inputPath: fakeFile("src"),
  name: faker.string.alpha(),
  options: {
    matches: [],
    matchAboutBlank: faker.datatype.boolean(),
    matchOriginAsFallback: faker.helpers.arrayElement([
      true,
      false,
      void 0
    ]),
    runAt: faker.helpers.arrayElement([
      "document_start",
      "document_end",
      "document_idle",
      void 0
    ])
  },
  outputDir: fakeDir(".output"),
  skipped: faker.datatype.boolean()
}));
export const fakeBackgroundEntrypoint = fakeObjectCreator(
  () => ({
    type: "background",
    inputPath: "entrypoints/background.ts",
    name: "background",
    options: {
      persistent: faker.datatype.boolean(),
      type: faker.helpers.maybe(() => "module")
    },
    outputDir: fakeDir(".output"),
    skipped: faker.datatype.boolean()
  })
);
export const fakeUnlistedScriptEntrypoint = fakeObjectCreator(() => ({
  type: "unlisted-script",
  inputPath: fakeFile("src"),
  name: faker.string.alpha(),
  outputDir: fakeDir(".output"),
  options: {},
  skipped: faker.datatype.boolean()
}));
export const fakeOptionsEntrypoint = fakeObjectCreator(
  () => ({
    type: "options",
    inputPath: "entrypoints/options.html",
    name: "options",
    outputDir: fakeDir(".output"),
    options: {
      browserStyle: faker.datatype.boolean(),
      chromeStyle: faker.datatype.boolean(),
      openInTab: faker.datatype.boolean()
    },
    skipped: faker.datatype.boolean()
  })
);
export const fakePopupEntrypoint = fakeObjectCreator(() => ({
  type: "popup",
  inputPath: "entrypoints/popup.html",
  name: "popup",
  outputDir: fakeDir(".output"),
  options: {
    defaultTitle: faker.helpers.arrayElement([
      faker.person.fullName(),
      void 0
    ]),
    defaultIcon: faker.helpers.arrayElement([
      {
        "16": "icon/16.png",
        "24": "icon/24.png",
        "64": "icon/64.png"
      }
    ]),
    mv2Key: faker.helpers.arrayElement([
      "browser_action",
      "page_action",
      void 0
    ])
  },
  skipped: faker.datatype.boolean()
}));
export const fakeSidepanelEntrypoint = fakeObjectCreator(
  () => ({
    type: "sidepanel",
    inputPath: "entrypoints/sidepanel.html",
    name: "sidepanel",
    outputDir: fakeDir(".output"),
    options: {
      defaultTitle: faker.helpers.arrayElement([
        faker.person.fullName(),
        void 0
      ]),
      defaultIcon: faker.helpers.arrayElement([
        {
          "16": "icon/16.png",
          "24": "icon/24.png",
          "64": "icon/64.png"
        }
      ]),
      openAtInstall: faker.helpers.arrayElement([true, false, void 0])
    },
    skipped: faker.datatype.boolean()
  })
);
export const fakeGenericEntrypoint = fakeObjectCreator(
  () => ({
    type: faker.helpers.arrayElement([
      "sandbox",
      "bookmarks",
      "history",
      "newtab",
      "devtools",
      "unlisted-page",
      "unlisted-script"
    ]),
    inputPath: fakeFile("src"),
    name: faker.string.alpha(),
    outputDir: fakeDir(".output"),
    options: {},
    skipped: faker.datatype.boolean()
  })
);
export const fakeOutputChunk = fakeObjectCreator(() => ({
  type: "chunk",
  fileName: faker.string.alphanumeric(),
  moduleIds: []
}));
export const fakeOutputAsset = fakeObjectCreator(() => ({
  type: "asset",
  fileName: fakeFileName()
}));
export function fakeOutputFile() {
  return faker.helpers.arrayElement([fakeOutputAsset(), fakeOutputChunk()]);
}
export const fakeManifest = fakeObjectCreator(
  () => ({
    manifest_version: faker.helpers.arrayElement([2, 3]),
    name: faker.string.alphanumeric(),
    version: `${faker.number.int()}.${faker.number.int()}.${faker.number.int()}`
  })
);
export const fakeUserManifest = fakeObjectCreator(() => ({
  name: faker.string.alphanumeric(),
  version: `${faker.number.int()}.${faker.number.int()}.${faker.number.int()}`
}));
export function fakeArray(createItem, count = 3) {
  const array = [];
  for (let i = 0; i < count; i++) {
    array.push(createItem());
  }
  return array;
}
export const fakeResolvedConfig = fakeObjectCreator(() => {
  const browser = faker.helpers.arrayElement(["chrome", "firefox"]);
  const command = faker.helpers.arrayElement(["build", "serve"]);
  const manifestVersion = faker.helpers.arrayElement([2, 3]);
  const mode = faker.helpers.arrayElement(["development", "production"]);
  return {
    browser,
    command,
    entrypointsDir: fakeDir(),
    modulesDir: fakeDir(),
    builtinModules: [],
    userModules: [],
    env: { browser, command, manifestVersion, mode },
    fsCache: mock(),
    imports: {
      eslintrc: {
        enabled: faker.helpers.arrayElement([false, 8, 9]),
        filePath: fakeFile(),
        globalsPropValue: faker.helpers.arrayElement([
          true,
          false,
          "readable",
          "readonly",
          "writable",
          "writeable"
        ])
      }
    },
    logger: mock(),
    manifest: fakeUserManifest(),
    manifestVersion,
    mode,
    outBaseDir: fakeDir(),
    outDir: fakeDir(),
    publicDir: fakeDir(),
    root: fakeDir(),
    wxtModuleDir: fakeDir(),
    runnerConfig: {
      config: {}
    },
    debug: faker.datatype.boolean(),
    srcDir: fakeDir(),
    typesDir: fakeDir(),
    wxtDir: fakeDir(),
    analysis: {
      enabled: false,
      open: false,
      template: "treemap",
      outputFile: fakeFile(),
      outputDir: fakeDir(),
      outputName: "stats",
      keepArtifacts: false
    },
    zip: {
      artifactTemplate: "{{name}}-{{version}}.zip",
      includeSources: [],
      excludeSources: [],
      exclude: [],
      sourcesRoot: fakeDir(),
      sourcesTemplate: "{{name}}-sources.zip",
      name: faker.person.firstName().toLowerCase(),
      downloadedPackagesDir: fakeDir(),
      downloadPackages: [],
      compressionLevel: 9,
      zipSources: false
    },
    transformManifest: () => {
    },
    userConfigMetadata: {},
    alias: {},
    extensionApi: "webextension-polyfill",
    entrypointLoader: "vite-node",
    experimental: {},
    dev: {
      reloadCommand: "Alt+R"
    },
    hooks: {},
    vite: () => ({}),
    plugins: []
  };
});
export const fakeWxt = fakeObjectCreator(() => ({
  config: fakeResolvedConfig(),
  hook: vi.fn(),
  hooks: mock(),
  logger: mock(),
  reloadConfig: vi.fn(),
  pm: mock(),
  server: faker.helpers.arrayElement([void 0, fakeWxtDevServer()]),
  builder: mock()
}));
export const fakeWxtDevServer = fakeObjectCreator(() => ({
  currentOutput: fakeBuildOutput(),
  hostname: "localhost",
  origin: "http://localhost:3000",
  port: 3e3,
  reloadContentScript: vi.fn(),
  reloadExtension: vi.fn(),
  reloadPage: vi.fn(),
  restart: vi.fn(),
  restartBrowser: vi.fn(),
  start: vi.fn(),
  stop: vi.fn(),
  transformHtml: vi.fn(),
  watcher: mock(),
  ws: mock()
}));
export function setFakeWxt(overrides) {
  const wxt = fakeWxt(overrides);
  setWxtForTesting(wxt);
  return wxt;
}
export const fakeBuildOutput = fakeObjectCreator(() => ({
  manifest: fakeManifest(),
  publicAssets: fakeArray(fakeOutputAsset),
  steps: fakeArray(fakeBuildStepOutput)
}));
export const fakeBuildStepOutput = fakeObjectCreator(() => ({
  chunks: fakeArray(fakeOutputChunk),
  entrypoints: fakeArray(fakeEntrypoint)
}));
export const fakeManifestCommand = fakeObjectCreator(() => ({
  description: faker.string.sample(),
  suggested_key: {
    default: `${faker.helpers.arrayElement(["ctrl", "alt"])}+${faker.number.int(
      {
        min: 0,
        max: 9
      }
    )}`
  }
}));
export const fakeDevServer = fakeObjectCreator(() => ({
  hostname: "localhost",
  origin: "http://localhost",
  port: 5173,
  reloadContentScript: vi.fn(),
  reloadExtension: vi.fn(),
  reloadPage: vi.fn(),
  restart: vi.fn(),
  restartBrowser: vi.fn(),
  stop: vi.fn(),
  start: vi.fn(),
  watcher: mock(),
  transformHtml: vi.fn(),
  ws: mock(),
  currentOutput: void 0
}));
