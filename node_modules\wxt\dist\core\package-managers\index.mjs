import {
  detectPackageManager,
  addDependency,
  addDevDependency,
  ensureDependencyInstalled,
  installDependencies,
  removeDependency
} from "nypm";
import { bun } from "./bun.mjs";
import { yarn } from "./yarn.mjs";
import { pnpm } from "./pnpm.mjs";
import { npm } from "./npm.mjs";
export async function createWxtPackageManager(root) {
  const pm = await detectPackageManager(root, {
    includeParentDirs: true
  });
  const requirePm = (cb) => {
    if (pm == null) throw Error("Could not detect package manager");
    return cb(pm);
  };
  return {
    get name() {
      return requirePm((pm2) => pm2.name);
    },
    get command() {
      return requirePm((pm2) => pm2.command);
    },
    get version() {
      return requirePm((pm2) => pm2.version);
    },
    get majorVersion() {
      return requirePm((pm2) => pm2.majorVersion);
    },
    get lockFile() {
      return requirePm((pm2) => pm2.lockFile);
    },
    get files() {
      return requirePm((pm2) => pm2.files);
    },
    addDependency,
    addDevDependency,
    ensureDependencyInstalled,
    installDependencies,
    removeDependency,
    get overridesKey() {
      return requirePm((pm2) => packageManagers[pm2.name].overridesKey);
    },
    downloadDependency(...args) {
      return requirePm(
        (pm2) => packageManagers[pm2.name].downloadDependency(...args)
      );
    },
    listDependencies(...args) {
      return requirePm(
        (pm2) => packageManagers[pm2.name].listDependencies(...args)
      );
    }
  };
}
const packageManagers = {
  npm,
  pnpm,
  bun,
  yarn
};
