import { loadConfig } from "c12";
import { resolve as esmResolve } from "import-meta-resolve";
import path from "node:path";
import { createFsCache } from "./utils/cache.mjs";
import consola, { LogLevels } from "consola";
import defu from "defu";
import fs from "fs-extra";
import { normalizePath } from "./utils/paths.mjs";
import glob from "fast-glob";
import { builtinModules } from "../builtin-modules/index.mjs";
import { getEslintVersion } from "./utils/eslint.mjs";
import { safeStringToNumber } from "./utils/number.mjs";
import { loadEnv } from "./utils/env.mjs";
import { getPort } from "get-port-please";
import { fileURLToPath, pathToFileURL } from "node:url";
export async function resolveConfig(inlineConfig, command) {
  let userConfig = {};
  let userConfigMetadata;
  if (inlineConfig.configFile !== false) {
    const { config: loadedConfig, ...metadata } = await loadConfig({
      configFile: inlineConfig.configFile,
      name: "wxt",
      cwd: inlineConfig.root ?? process.cwd(),
      rcFile: false
    });
    if (inlineConfig.configFile && metadata.layers?.length === 0) {
      throw Error(`Config file "${inlineConfig.configFile}" not found`);
    }
    userConfig = loadedConfig ?? {};
    userConfigMetadata = metadata;
  }
  const mergedConfig = await mergeInlineConfig(inlineConfig, userConfig);
  const debug = mergedConfig.debug ?? false;
  const logger = mergedConfig.logger ?? consola;
  if (debug) logger.level = LogLevels.debug;
  const browser = mergedConfig.browser ?? "chrome";
  const manifestVersion = mergedConfig.manifestVersion ?? (browser === "firefox" || browser === "safari" ? 2 : 3);
  const mode = mergedConfig.mode ?? COMMAND_MODES[command];
  const env = { browser, command, manifestVersion, mode };
  loadEnv(mode, browser);
  const root = path.resolve(
    inlineConfig.root ?? userConfig.root ?? process.cwd()
  );
  const wxtDir = path.resolve(root, ".wxt");
  const wxtModuleDir = resolveWxtModuleDir();
  const srcDir = path.resolve(root, mergedConfig.srcDir ?? root);
  const entrypointsDir = path.resolve(
    srcDir,
    mergedConfig.entrypointsDir ?? "entrypoints"
  );
  const modulesDir = path.resolve(srcDir, mergedConfig.modulesDir ?? "modules");
  if (await isDirMissing(entrypointsDir)) {
    logMissingDir(logger, "Entrypoints", entrypointsDir);
  }
  const filterEntrypoints = mergedConfig.filterEntrypoints?.length ? new Set(mergedConfig.filterEntrypoints) : void 0;
  const publicDir = path.resolve(srcDir, mergedConfig.publicDir ?? "public");
  const typesDir = path.resolve(wxtDir, "types");
  const outBaseDir = path.resolve(root, mergedConfig.outDir ?? ".output");
  const modeSuffixes = {
    production: "",
    development: "-dev"
  };
  const outDirTemplate = (mergedConfig.outDirTemplate ?? `${browser}-mv${manifestVersion}`).replaceAll("{{browser}}", browser).replaceAll("{{manifestVersion}}", manifestVersion.toString()).replaceAll("{{modeSuffix}}", modeSuffixes[mode] ?? `-${mode}`).replaceAll("{{mode}}", mode).replaceAll("{{command}}", command);
  const outDir = path.resolve(outBaseDir, outDirTemplate);
  const reloadCommand = mergedConfig.dev?.reloadCommand ?? "Alt+R";
  const runnerConfig = await loadConfig({
    name: "web-ext",
    cwd: root,
    globalRc: true,
    rcFile: ".webextrc",
    overrides: inlineConfig.runner,
    defaults: userConfig.runner
  });
  const alias = Object.fromEntries(
    Object.entries({
      ...mergedConfig.alias,
      "@": srcDir,
      "~": srcDir,
      "@@": root,
      "~~": root
    }).map(([key, value]) => [key, path.resolve(root, value)])
  );
  let devServerConfig;
  if (command === "serve") {
    const hostname = mergedConfig.dev?.server?.hostname ?? "localhost";
    let port = mergedConfig.dev?.server?.port;
    if (port == null || !isFinite(port)) {
      port = await getPort({
        port: 3e3,
        portRange: [3001, 3010],
        // Passing host required for Mac, unsure of Windows/Linux
        host: hostname
      });
    }
    devServerConfig = {
      port,
      hostname,
      watchDebounce: safeStringToNumber(process.env.WXT_WATCH_DEBOUNCE) ?? 800
    };
  }
  const userModules = await resolveWxtUserModules(
    root,
    modulesDir,
    mergedConfig.modules
  );
  const moduleOptions = userModules.reduce(
    (map, module) => {
      if (module.configKey) {
        map[module.configKey] = // @ts-expect-error
        mergedConfig[module.configKey];
      }
      return map;
    },
    {}
  );
  const extensionApi = mergedConfig.extensionApi ?? "webextension-polyfill";
  return {
    browser,
    command,
    debug,
    entrypointsDir,
    modulesDir,
    filterEntrypoints,
    env,
    fsCache: createFsCache(wxtDir),
    imports: await getUnimportOptions(
      wxtDir,
      srcDir,
      logger,
      extensionApi,
      mergedConfig
    ),
    logger,
    manifest: await resolveManifestConfig(env, mergedConfig.manifest),
    manifestVersion,
    mode,
    outBaseDir,
    outDir,
    publicDir,
    wxtModuleDir,
    root,
    runnerConfig,
    srcDir,
    typesDir,
    wxtDir,
    zip: resolveZipConfig(root, browser, outBaseDir, mergedConfig),
    transformManifest: mergedConfig.transformManifest,
    analysis: resolveAnalysisConfig(root, mergedConfig),
    userConfigMetadata: userConfigMetadata ?? {},
    alias,
    extensionApi,
    entrypointLoader: mergedConfig.entrypointLoader ?? "vite-node",
    experimental: defu(mergedConfig.experimental, {}),
    dev: {
      server: devServerConfig,
      reloadCommand
    },
    hooks: mergedConfig.hooks ?? {},
    vite: mergedConfig.vite ?? (() => ({})),
    builtinModules,
    userModules,
    plugins: [],
    ...moduleOptions
  };
}
async function resolveManifestConfig(env, manifest) {
  return typeof manifest === "function" ? await manifest(env) : await (manifest ?? {});
}
async function mergeInlineConfig(inlineConfig, userConfig) {
  const imports = inlineConfig.imports === false || userConfig.imports === false ? false : userConfig.imports == null && inlineConfig.imports == null ? void 0 : defu(inlineConfig.imports ?? {}, userConfig.imports ?? {});
  const manifest = async (env) => {
    const user = await resolveManifestConfig(env, userConfig.manifest);
    const inline = await resolveManifestConfig(env, inlineConfig.manifest);
    return defu(inline, user);
  };
  const transformManifest = (manifest2) => {
    userConfig.transformManifest?.(manifest2);
    inlineConfig.transformManifest?.(manifest2);
  };
  const merged = defu(inlineConfig, userConfig);
  const builderConfig = await mergeBuilderConfig(
    merged.logger ?? consola,
    inlineConfig,
    userConfig
  );
  return {
    ...merged,
    // Custom merge values
    transformManifest,
    imports,
    manifest,
    ...builderConfig
  };
}
function resolveZipConfig(root, browser, outBaseDir, mergedConfig) {
  const downloadedPackagesDir = path.resolve(root, ".wxt/local_modules");
  return {
    name: void 0,
    sourcesTemplate: "{{name}}-{{version}}-sources.zip",
    artifactTemplate: "{{name}}-{{version}}-{{browser}}.zip",
    sourcesRoot: root,
    includeSources: [],
    compressionLevel: 9,
    ...mergedConfig.zip,
    zipSources: mergedConfig.zip?.zipSources ?? ["firefox", "opera"].includes(browser),
    exclude: mergedConfig.zip?.exclude ?? [],
    excludeSources: [
      "**/node_modules",
      // WXT files
      "**/web-ext.config.ts",
      // Hidden files
      "**/.*",
      // Tests
      "**/__tests__/**",
      "**/*.+(test|spec).?(c|m)+(j|t)s?(x)",
      // Output directory
      `${path.relative(root, outBaseDir)}/**`,
      // From user
      ...mergedConfig.zip?.excludeSources ?? []
    ],
    downloadPackages: mergedConfig.zip?.downloadPackages ?? [],
    downloadedPackagesDir
  };
}
function resolveAnalysisConfig(root, mergedConfig) {
  const analysisOutputFile = path.resolve(
    root,
    mergedConfig.analysis?.outputFile ?? "stats.html"
  );
  const analysisOutputDir = path.dirname(analysisOutputFile);
  const analysisOutputName = path.parse(analysisOutputFile).name;
  return {
    enabled: mergedConfig.analysis?.enabled ?? false,
    open: mergedConfig.analysis?.open ?? false,
    template: mergedConfig.analysis?.template ?? "treemap",
    outputFile: analysisOutputFile,
    outputDir: analysisOutputDir,
    outputName: analysisOutputName,
    keepArtifacts: mergedConfig.analysis?.keepArtifacts ?? false
  };
}
async function getUnimportOptions(wxtDir, srcDir, logger, extensionApi, config) {
  if (config.imports === false) return false;
  const defaultOptions = {
    debugLog: logger.debug,
    imports: [
      { name: "defineConfig", from: "wxt" },
      { name: "fakeBrowser", from: "wxt/testing" }
    ],
    presets: [
      {
        package: "wxt/client",
        // There seems to be a bug in unimport that thinks "options" is an
        // export from wxt/client, but it doesn't actually exist... so it's
        // ignored.
        ignore: ["options"]
      },
      {
        package: extensionApi === "chrome" ? "wxt/browser/chrome" : "wxt/browser"
      },
      { package: "wxt/sandbox" },
      { package: "wxt/storage" }
    ],
    warn: logger.warn,
    dirs: ["components", "composables", "hooks", "utils"],
    dirsScanOptions: {
      cwd: srcDir
    },
    eslintrc: await getUnimportEslintOptions(wxtDir, config.imports?.eslintrc)
  };
  return defu(
    config.imports ?? {},
    defaultOptions
  );
}
async function getUnimportEslintOptions(wxtDir, options) {
  const rawEslintEnabled = options?.enabled ?? "auto";
  let eslintEnabled;
  switch (rawEslintEnabled) {
    case "auto":
      const version = await getEslintVersion();
      let major = parseInt(version[0]);
      if (isNaN(major)) eslintEnabled = false;
      if (major <= 8) eslintEnabled = 8;
      else if (major >= 9) eslintEnabled = 9;
      else eslintEnabled = false;
      break;
    case true:
      eslintEnabled = 8;
      break;
    default:
      eslintEnabled = rawEslintEnabled;
  }
  return {
    enabled: eslintEnabled,
    filePath: path.resolve(
      wxtDir,
      eslintEnabled === 9 ? "eslint-auto-imports.mjs" : "eslintrc-auto-import.json"
    ),
    globalsPropValue: true
  };
}
function resolveWxtModuleDir() {
  const importer = typeof __filename === "string" ? pathToFileURL(__filename).href : import.meta.url;
  const url = esmResolve("wxt", importer);
  return path.resolve(fileURLToPath(url), "../..");
}
async function isDirMissing(dir) {
  return !await fs.exists(dir);
}
function logMissingDir(logger, name, expected) {
  logger.warn(
    `${name} directory not found: ./${normalizePath(
      path.relative(process.cwd(), expected)
    )}`
  );
}
const COMMAND_MODES = {
  build: "production",
  serve: "development"
};
export async function mergeBuilderConfig(logger, inlineConfig, userConfig) {
  const vite = await import("vite").catch((err) => {
    logger.debug("Failed to import vite:", err);
  });
  if (vite) {
    return {
      vite: async (env) => {
        const resolvedInlineConfig = await inlineConfig.vite?.(env) ?? {};
        const resolvedUserConfig = await userConfig.vite?.(env) ?? {};
        return vite.mergeConfig(resolvedUserConfig, resolvedInlineConfig);
      }
    };
  }
  throw Error("Builder not found. Make sure vite is installed.");
}
export async function resolveWxtUserModules(root, modulesDir, modules = []) {
  const importer = pathToFileURL(path.join(root, "index.js")).href;
  const npmModules = await Promise.all(
    modules.map(async (moduleId) => {
      const resolvedModulePath = esmResolve(moduleId, importer);
      const mod = await import(
        /* @vite-ignore */
        resolvedModulePath
      );
      if (mod.default == null) {
        throw Error("Module missing default export: " + moduleId);
      }
      return {
        ...mod.default,
        type: "node_module",
        id: moduleId
      };
    })
  );
  const localModulePaths = await glob(["*.[tj]s", "*/index.[tj]s"], {
    cwd: modulesDir,
    onlyFiles: true
  }).catch(() => []);
  localModulePaths.sort();
  const localModules = await Promise.all(
    localModulePaths.map(async (file) => {
      const absolutePath = normalizePath(path.resolve(modulesDir, file));
      const { config } = await loadConfig({
        configFile: absolutePath,
        globalRc: false,
        rcFile: false,
        packageJson: false,
        envName: false,
        dotenv: false
      });
      if (config == null)
        throw Error(
          `No config found for ${file}. Did you forget to add a default export?`
        );
      config.name ??= file;
      return {
        ...config,
        type: "local",
        id: absolutePath
      };
    })
  );
  return [...npmModules, ...localModules];
}
