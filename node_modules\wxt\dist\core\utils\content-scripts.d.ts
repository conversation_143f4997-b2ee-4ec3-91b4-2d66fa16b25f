import type { <PERSON>ife<PERSON>, Scrip<PERSON> } from 'wxt/browser';
import { ContentScriptEntrypoint, ResolvedConfig } from '../../types';
/**
 * Returns a unique and consistent string hash based on a content scripts options.
 *
 * It is able to recognize default values,
 */
export declare function hashContentScriptOptions(options: ContentScriptEntrypoint['options']): string;
export declare function mapWxtOptionsToContentScript(options: ContentScriptEntrypoint['options'], js: string[] | undefined, css: string[] | undefined): Manifest.ContentScript;
export declare function mapWxtOptionsToRegisteredContentScript(options: ContentScriptEntrypoint['options'], js: string[] | undefined, css: string[] | undefined): Omit<Scripting.RegisteredContentScript, 'id'>;
export declare function getContentScriptJs(config: ResolvedConfig, entrypoint: ContentScriptEntrypoint): string[];
