import type * as vite from 'vite';
import { Entrypoint, ResolvedConfig } from '../../../../types';
/**
 * Rename CSS entrypoint outputs to ensure a JS file is not generated, and that the CSS file is
 * placed in the correct place.
 *
 * It:
 * 1. Renames CSS files to their final paths
 * 2. Removes the JS file that get's output by lib mode
 *
 * THIS PLUGIN SHOULD ONLY BE APPLIED TO CSS LIB MODE BUILDS. It should not be added to every build.
 */
export declare function cssEntrypoints(entrypoint: Entrypoint, config: ResolvedConfig): vite.Plugin;
