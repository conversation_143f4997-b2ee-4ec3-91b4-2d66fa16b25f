# <em>µ</em>hyphen

[![Build Status](https://travis-ci.com/WebReflection/uhyphen.svg?branch=master)](https://travis-ci.com/WebReflection/uhyphen) [![Coverage Status](https://coveralls.io/repos/github/WebReflection/uhyphen/badge.svg?branch=master)](https://coveralls.io/github/WebReflection/uhyphen?branch=master)

A <em>micro</em> utility to hyphenize strings.

```js
import uhyphen from 'uhyphen';
// const uhyphen = require('uhyphen');

uhyphen('XMLHttpRequest');
// xml-http-request
```
