import { ContentScriptContext } from '.';
/**
 * Create a util that watches for URL changes, dispatching the custom event when detected. Stops
 * watching when content script is invalidated.
 */
export declare function createLocationWatcher(ctx: ContentScriptContext): {
    /**
     * Ensure the location watcher is actively looking for URL changes. If it's already watching,
     * this is a noop.
     */
    run(): void;
};
