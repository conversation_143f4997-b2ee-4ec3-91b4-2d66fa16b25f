import type { EslintGlobalsPropValue, WxtDirFileEntry, WxtModule, WxtResolvedUnimportOptions } from '../types';
declare const _default: WxtModule<import("../types").WxtModuleOptions>;
export default _default;
export declare function getEslint8ConfigEntry(options: WxtResolvedUnimportOptions, globals: Record<string, EslintGlobalsPropValue>): WxtDirFileEntry;
export declare function getEslint9ConfigEntry(options: WxtResolvedUnimportOptions, globals: Record<string, EslintGlobalsPropValue>): WxtDirFileEntry;
