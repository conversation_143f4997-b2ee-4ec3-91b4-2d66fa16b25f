import "../assets/youtube.css";

export default defineContentScript({
  matches: ["*://www.youtube.com/*", "*://youtube.com/*"],
  main() {
    console.log("YouTube Focus Extension loaded");

    // YouTube Focus Extension Main Logic
    class YouTubeFocusExtension {
      private modal: HTMLElement | null = null;
      private isHomepage = false;
      private isVideoPage = false;

      constructor() {
        this.init();
      }

      private init() {
        // Wait for page to load
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", () =>
            this.handlePageLoad()
          );
        } else {
          this.handlePageLoad();
        }

        // Handle navigation changes (YouTube is SPA)
        this.observeUrlChanges();
      }

      private handlePageLoad() {
        this.detectPageType();

        if (this.isHomepage) {
          this.handleHomepage();
        } else if (this.isVideoPage) {
          this.handleVideoPage();
        }
      }

      private detectPageType() {
        const url = window.location.href;
        const pathname = window.location.pathname;

        this.isHomepage =
          pathname === "/" || pathname === "" || url.includes("youtube.com/?");
        this.isVideoPage = pathname.includes("/watch") && url.includes("v=");
      }

      private observeUrlChanges() {
        let lastUrl = location.href;
        new MutationObserver(() => {
          const url = location.href;
          if (url !== lastUrl) {
            lastUrl = url;
            setTimeout(() => this.handlePageLoad(), 500); // Delay to let YouTube load content
          }
        }).observe(document, { subtree: true, childList: true });
      }

      private handleHomepage() {
        console.log("Handling homepage");
        this.showSearchModal();
      }

      private handleVideoPage() {
        console.log("Handling video page");
        this.hideRecommendations();
      }

      private showSearchModal() {
        // Remove existing modal if present
        if (this.modal) {
          this.modal.remove();
        }

        // Create modal
        this.modal = document.createElement("div");
        this.modal.className = "youtube-focus-modal";
        this.modal.innerHTML = `
          <div class="youtube-focus-modal-content">
            <div class="youtube-focus-modal-header">
              <h2>Search YouTube Videos</h2>
              <button class="youtube-focus-close-btn">&times;</button>
            </div>
            <div class="youtube-focus-modal-body">
              <div class="youtube-focus-search-container">
                <input type="text" id="youtube-focus-search" placeholder="Search for videos..." />
                <button id="youtube-focus-search-btn">Search</button>
              </div>
              <div id="youtube-focus-results" class="youtube-focus-results"></div>
            </div>
          </div>
        `;

        document.body.appendChild(this.modal);

        // Add event listeners
        this.setupModalEventListeners();
      }

      private setupModalEventListeners() {
        if (!this.modal) return;

        const closeBtn = this.modal.querySelector(".youtube-focus-close-btn");
        const searchBtn = this.modal.querySelector("#youtube-focus-search-btn");
        const searchInput = this.modal.querySelector(
          "#youtube-focus-search"
        ) as HTMLInputElement;

        closeBtn?.addEventListener("click", () => this.closeModal());
        searchBtn?.addEventListener("click", () => this.performSearch());
        searchInput?.addEventListener("keypress", (e) => {
          if (e.key === "Enter") {
            this.performSearch();
          }
        });

        // Close modal when clicking outside
        this.modal.addEventListener("click", (e) => {
          if (e.target === this.modal) {
            this.closeModal();
          }
        });
      }

      private closeModal() {
        if (this.modal) {
          this.modal.remove();
          this.modal = null;
        }
      }

      private async performSearch() {
        const searchInput = document.querySelector(
          "#youtube-focus-search"
        ) as HTMLInputElement;
        const resultsContainer = document.querySelector(
          "#youtube-focus-results"
        );

        if (!searchInput || !resultsContainer) return;

        const query = searchInput.value.trim();
        if (!query) return;

        resultsContainer.innerHTML =
          '<div class="youtube-focus-loading">Searching...</div>';

        try {
          // Use YouTube's search by redirecting to search results
          const searchUrl = `https://www.youtube.com/results?search_query=${encodeURIComponent(
            query
          )}`;
          window.location.href = searchUrl;
        } catch (error) {
          resultsContainer.innerHTML =
            '<div class="youtube-focus-error">Search failed. Please try again.</div>';
        }
      }

      private hideRecommendations() {
        // Hide sidebar recommendations
        const selectors = [
          "#secondary", // Main sidebar
          "#related", // Related videos
          ".ytd-watch-next-secondary-results-renderer", // Watch next
          ".ytp-endscreen-content", // End screen
          ".ytp-ce-element", // Cards
          "#comments", // Comments (optional - remove if you want to keep comments)
          ".ytd-compact-video-renderer", // Compact video recommendations
          ".ytd-video-secondary-info-renderer #meta-contents #container", // Video info recommendations
        ];

        selectors.forEach((selector) => {
          const elements = document.querySelectorAll(selector);
          elements.forEach((element) => {
            (element as HTMLElement).style.display = "none";
          });
        });

        // Also hide autoplay toggle
        const autoplayToggle = document.querySelector(
          ".ytp-autonav-toggle-button"
        );
        if (autoplayToggle) {
          (autoplayToggle as HTMLElement).style.display = "none";
        }

        // Observe for dynamically loaded content
        this.observeRecommendations();
      }

      private observeRecommendations() {
        const observer = new MutationObserver(() => {
          this.hideRecommendations();
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
        });
      }
    }

    // Initialize the extension
    new YouTubeFocusExtension();
  },
});
