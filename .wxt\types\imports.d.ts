// Generated by wxt
export {}
declare global {
  const ContentScriptContext: typeof import('wxt/client')['ContentScriptContext']
  const InvalidMatchPattern: typeof import('wxt/sandbox')['InvalidMatchPattern']
  const MatchPattern: typeof import('wxt/sandbox')['MatchPattern']
  const MigrationError: typeof import('wxt/storage')['MigrationError']
  const browser: typeof import('wxt/browser/chrome')['browser']
  const createIframeUi: typeof import('wxt/client')['createIframeUi']
  const createIntegratedUi: typeof import('wxt/client')['createIntegratedUi']
  const createShadowRootUi: typeof import('wxt/client')['createShadowRootUi']
  const defineAppConfig: typeof import('wxt/sandbox')['defineAppConfig']
  const defineBackground: typeof import('wxt/sandbox')['defineBackground']
  const defineConfig: typeof import('wxt')['defineConfig']
  const defineContentScript: typeof import('wxt/sandbox')['defineContentScript']
  const defineUnlistedScript: typeof import('wxt/sandbox')['defineUnlistedScript']
  const defineWxtPlugin: typeof import('wxt/sandbox')['defineWxtPlugin']
  const fakeBrowser: typeof import('wxt/testing')['fakeBrowser']
  const injectScript: typeof import('wxt/client')['injectScript']
  const storage: typeof import('wxt/storage')['storage']
  const useAppConfig: typeof import('wxt/client')['useAppConfig']
}
