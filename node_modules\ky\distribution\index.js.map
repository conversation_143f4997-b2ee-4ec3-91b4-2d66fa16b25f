{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../source/index.ts"], "names": [], "mappings": "AAAA,kCAAkC;AAElC,OAAO,EAAC,EAAE,EAAC,MAAM,cAAc,CAAC;AAChC,OAAO,EAAC,cAAc,EAAE,IAAI,EAAC,MAAM,qBAAqB,CAAC;AAGzD,OAAO,EAAC,gBAAgB,EAAC,MAAM,kBAAkB,CAAC;AAGlD,MAAM,cAAc,GAAG,CAAC,QAA2B,EAAc,EAAE;IAClE,qEAAqE;IACrE,MAAM,EAAE,GAAiC,CAAC,KAAY,EAAE,OAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC;IAEpI,KAAK,MAAM,MAAM,IAAI,cAAc,EAAE,CAAC;QACrC,qEAAqE;QACrE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,KAAY,EAAE,OAAiB,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAgB,CAAC,QAAQ,EAAE,OAAO,EAAE,EAAC,MAAM,EAAC,CAAC,CAAC,CAAC;IACnH,CAAC;IAED,EAAE,CAAC,MAAM,GAAG,CAAC,WAA8B,EAAE,EAAE,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;IAC9F,EAAE,CAAC,MAAM,GAAG,CAAC,WAAyF,EAAE,EAAE;QACzG,IAAI,OAAO,WAAW,KAAK,UAAU,EAAE,CAAC;YACvC,WAAW,GAAG,WAAW,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;IAChE,CAAC,CAAC;IAEF,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;IAEf,OAAO,EAAgB,CAAC;AACzB,CAAC,CAAC;AAEF,MAAM,EAAE,GAAG,cAAc,EAAE,CAAC;AAE5B,eAAe,EAAE,CAAC;AAyBlB,OAAO,EAAC,SAAS,EAAC,MAAM,uBAAuB,CAAC;AAChD,OAAO,EAAC,YAAY,EAAC,MAAM,0BAA0B,CAAC", "sourcesContent": ["/*! MIT License © Sindre Sorhus */\n\nimport {Ky} from './core/Ky.js';\nimport {requestMethods, stop} from './core/constants.js';\nimport type {KyInstance} from './types/ky.js';\nimport type {Input, Options} from './types/options.js';\nimport {validateAndMerge} from './utils/merge.js';\nimport {type Mutable} from './utils/types.js';\n\nconst createInstance = (defaults?: Partial<Options>): KyInstance => {\n\t// eslint-disable-next-line @typescript-eslint/promise-function-async\n\tconst ky: Partial<Mutable<KyInstance>> = (input: Input, options?: Options) => Ky.create(input, validateAndMerge(defaults, options));\n\n\tfor (const method of requestMethods) {\n\t\t// eslint-disable-next-line @typescript-eslint/promise-function-async\n\t\tky[method] = (input: Input, options?: Options) => Ky.create(input, validateAndMerge(defaults, options, {method}));\n\t}\n\n\tky.create = (newDefaults?: Partial<Options>) => createInstance(validateAndMerge(newDefaults));\n\tky.extend = (newDefaults?: Partial<Options> | ((parentDefaults: Partial<Options>) => Partial<Options>)) => {\n\t\tif (typeof newDefaults === 'function') {\n\t\t\tnewDefaults = newDefaults(defaults ?? {});\n\t\t}\n\n\t\treturn createInstance(validateAndMerge(defaults, newDefaults));\n\t};\n\n\tky.stop = stop;\n\n\treturn ky as KyInstance;\n};\n\nconst ky = createInstance();\n\nexport default ky;\n\nexport type {KyInstance} from './types/ky.js';\n\nexport type {\n\tInput,\n\tOptions,\n\tNormalizedOptions,\n\tRetryOptions,\n\tSearchParamsOption,\n\tProgress,\n} from './types/options.js';\n\nexport type {\n\tHooks,\n\tBeforeRequestHook,\n\tBeforeRetryHook,\n\tBeforeRetryState,\n\tBeforeErrorHook,\n\tAfterResponseHook,\n} from './types/hooks.js';\n\nexport type {ResponsePromise} from './types/ResponsePromise.js';\nexport type {KyRequest} from './types/request.js';\nexport type {KyResponse} from './types/response.js';\nexport {HTTPError} from './errors/HTTPError.js';\nexport {TimeoutError} from './errors/TimeoutError.js';\n"]}