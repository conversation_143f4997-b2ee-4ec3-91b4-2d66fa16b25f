import { browser } from "wxt/browser";
import { waitElement } from "@1natsu/wait-element";
import {
  isExist as mountDetector,
  isNotExist as removeDetector
} from "@1natsu/wait-element/detectors";
import { logger } from "../../../sandbox/utils/logger.mjs";
import { createIsolatedElement } from "@webext-core/isolated-element";
export * from "./types.mjs";
export function createIntegratedUi(ctx, options) {
  const wrapper = document.createElement(options.tag || "div");
  wrapper.setAttribute("data-wxt-integrated", "");
  let mounted = void 0;
  const mount = () => {
    applyPosition(wrapper, void 0, options);
    mountUi(wrapper, options);
    mounted = options.onMount?.(wrapper);
  };
  const remove = () => {
    options.onRemove?.(mounted);
    wrapper.replaceChildren();
    wrapper.remove();
    mounted = void 0;
  };
  const mountFunctions = createMountFunctions(
    {
      mount,
      remove
    },
    options
  );
  ctx.onInvalidated(remove);
  return {
    get mounted() {
      return mounted;
    },
    wrapper,
    ...mountFunctions
  };
}
export function createIframeUi(ctx, options) {
  const wrapper = document.createElement("div");
  wrapper.setAttribute("data-wxt-iframe", "");
  const iframe = document.createElement("iframe");
  iframe.src = browser.runtime.getURL(options.page);
  wrapper.appendChild(iframe);
  let mounted = void 0;
  const mount = () => {
    applyPosition(wrapper, iframe, options);
    mountUi(wrapper, options);
    mounted = options.onMount?.(wrapper, iframe);
  };
  const remove = () => {
    options.onRemove?.(mounted);
    wrapper.remove();
    mounted = void 0;
  };
  const mountFunctions = createMountFunctions(
    {
      mount,
      remove
    },
    options
  );
  ctx.onInvalidated(remove);
  return {
    get mounted() {
      return mounted;
    },
    iframe,
    wrapper,
    ...mountFunctions
  };
}
export async function createShadowRootUi(ctx, options) {
  const css = [options.css ?? ""];
  if (ctx.options?.cssInjectionMode === "ui") {
    const entryCss = await loadCss();
    css.push(entryCss.replaceAll(":root", ":host"));
  }
  const {
    isolatedElement: uiContainer,
    parentElement: shadowHost,
    shadow
  } = await createIsolatedElement({
    name: options.name,
    css: {
      textContent: css.join("\n").trim()
    },
    mode: options.mode ?? "open",
    isolateEvents: options.isolateEvents
  });
  shadowHost.setAttribute("data-wxt-shadow-root", "");
  let mounted;
  const mount = () => {
    mountUi(shadowHost, options);
    applyPosition(shadowHost, shadow.querySelector("html"), options);
    mounted = options.onMount(uiContainer, shadow, shadowHost);
  };
  const remove = () => {
    options.onRemove?.(mounted);
    shadowHost.remove();
    while (uiContainer.lastChild)
      uiContainer.removeChild(uiContainer.lastChild);
    mounted = void 0;
  };
  const mountFunctions = createMountFunctions(
    {
      mount,
      remove
    },
    options
  );
  ctx.onInvalidated(remove);
  return {
    shadow,
    shadowHost,
    uiContainer,
    ...mountFunctions,
    get mounted() {
      return mounted;
    }
  };
}
function applyPosition(root, positionedElement, options) {
  if (options.position === "inline") return;
  if (options.zIndex != null) root.style.zIndex = String(options.zIndex);
  root.style.overflow = "visible";
  root.style.position = "relative";
  root.style.width = "0";
  root.style.height = "0";
  root.style.display = "block";
  if (positionedElement) {
    if (options.position === "overlay") {
      positionedElement.style.position = "absolute";
      if (options.alignment?.startsWith("bottom-"))
        positionedElement.style.bottom = "0";
      else positionedElement.style.top = "0";
      if (options.alignment?.endsWith("-right"))
        positionedElement.style.right = "0";
      else positionedElement.style.left = "0";
    } else {
      positionedElement.style.position = "fixed";
      positionedElement.style.top = "0";
      positionedElement.style.bottom = "0";
      positionedElement.style.left = "0";
      positionedElement.style.right = "0";
    }
  }
}
function getAnchor(options) {
  if (options.anchor == null) return document.body;
  let resolved = typeof options.anchor === "function" ? options.anchor() : options.anchor;
  if (typeof resolved === "string") {
    if (resolved.startsWith("/")) {
      const result = document.evaluate(
        resolved,
        document,
        null,
        XPathResult.FIRST_ORDERED_NODE_TYPE,
        null
      );
      return result.singleNodeValue ?? void 0;
    } else {
      return document.querySelector(resolved) ?? void 0;
    }
  }
  return resolved ?? void 0;
}
function mountUi(root, options) {
  const anchor = getAnchor(options);
  if (anchor == null)
    throw Error(
      "Failed to mount content script UI: could not find anchor element"
    );
  switch (options.append) {
    case void 0:
    case "last":
      anchor.append(root);
      break;
    case "first":
      anchor.prepend(root);
      break;
    case "replace":
      anchor.replaceWith(root);
      break;
    case "after":
      anchor.parentElement?.insertBefore(root, anchor.nextElementSibling);
      break;
    case "before":
      anchor.parentElement?.insertBefore(root, anchor);
      break;
    default:
      options.append(anchor, root);
      break;
  }
}
function createMountFunctions(baseFunctions, options) {
  let autoMountInstance = void 0;
  const stopAutoMount = () => {
    autoMountInstance?.stopAutoMount();
    autoMountInstance = void 0;
  };
  const mount = () => {
    baseFunctions.mount();
  };
  const unmount = baseFunctions.remove;
  const remove = () => {
    stopAutoMount();
    baseFunctions.remove();
  };
  const autoMount = (autoMountOptions) => {
    if (autoMountInstance) {
      logger.warn("autoMount is already set.");
    }
    autoMountInstance = autoMountUi(
      { mount, unmount, stopAutoMount },
      {
        ...options,
        ...autoMountOptions
      }
    );
  };
  return {
    mount,
    remove,
    autoMount
  };
}
function autoMountUi(uiCallbacks, options) {
  const abortController = new AbortController();
  const EXPLICIT_STOP_REASON = "explicit_stop_auto_mount";
  const _stopAutoMount = () => {
    abortController.abort(EXPLICIT_STOP_REASON);
    options.onStop?.();
  };
  let resolvedAnchor = typeof options.anchor === "function" ? options.anchor() : options.anchor;
  if (resolvedAnchor instanceof Element) {
    throw Error(
      "autoMount and Element anchor option cannot be combined. Avoid passing `Element` directly or `() => Element` to the anchor."
    );
  }
  async function observeElement(selector) {
    let isAnchorExist = !!getAnchor(options);
    if (isAnchorExist) {
      uiCallbacks.mount();
    }
    while (!abortController.signal.aborted) {
      try {
        const changedAnchor = await waitElement(selector ?? "body", {
          customMatcher: () => getAnchor(options) ?? null,
          detector: isAnchorExist ? removeDetector : mountDetector,
          signal: abortController.signal
        });
        isAnchorExist = !!changedAnchor;
        if (isAnchorExist) {
          uiCallbacks.mount();
        } else {
          uiCallbacks.unmount();
          if (options.once) {
            uiCallbacks.stopAutoMount();
          }
        }
      } catch (error) {
        if (abortController.signal.aborted && abortController.signal.reason === EXPLICIT_STOP_REASON) {
          break;
        } else {
          throw error;
        }
      }
    }
  }
  observeElement(resolvedAnchor);
  return { stopAutoMount: _stopAutoMount };
}
async function loadCss() {
  const url = browser.runtime.getURL(
    `/content-scripts/${import.meta.env.ENTRYPOINT}.css`
  );
  try {
    const res = await fetch(url);
    return await res.text();
  } catch (err) {
    logger.warn(
      `Failed to load styles @ ${url}. Did you forget to import the stylesheet in your entrypoint?`,
      err
    );
    return "";
  }
}
