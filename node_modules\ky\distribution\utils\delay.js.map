{"version": 3, "file": "delay.js", "sourceRoot": "", "sources": ["../../source/utils/delay.ts"], "names": [], "mappings": "AAAA,sFAAsF;AAQtF,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,KAAK,CAClC,EAAU,EACV,EAAC,MAAM,EAAe;IAEtB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACtC,IAAI,MAAM,EAAE,CAAC;YACZ,MAAM,CAAC,cAAc,EAAE,CAAC;YACxB,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,YAAY,EAAE,EAAC,IAAI,EAAE,IAAI,EAAC,CAAC,CAAC;QAC9D,CAAC;QAED,SAAS,YAAY;YACpB,YAAY,CAAC,SAAS,CAAC,CAAC;YACxB,MAAM,CAAC,MAAO,CAAC,MAAe,CAAC,CAAC;QACjC,CAAC;QAED,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;YACjC,MAAM,EAAE,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;YACnD,OAAO,EAAE,CAAC;QACX,CAAC,EAAE,EAAE,CAAC,CAAC;IACR,CAAC,CAAC,CAAC;AACJ,CAAC", "sourcesContent": ["// https://github.com/sindresorhus/delay/tree/ab98ae8dfcb38e1593286c94d934e70d14a4e111\n\nimport {type InternalOptions} from '../types/options.js';\n\nexport type DelayOptions = {\n\tsignal?: InternalOptions['signal'];\n};\n\nexport default async function delay(\n\tms: number,\n\t{signal}: DelayOptions,\n): Promise<void> {\n\treturn new Promise((resolve, reject) => {\n\t\tif (signal) {\n\t\t\tsignal.throwIfAborted();\n\t\t\tsignal.addEventListener('abort', abortHandler, {once: true});\n\t\t}\n\n\t\tfunction abortHandler() {\n\t\t\tclearTimeout(timeoutId);\n\t\t\treject(signal!.reason as Error);\n\t\t}\n\n\t\tconst timeoutId = setTimeout(() => {\n\t\t\tsignal?.removeEventListener('abort', abortHandler);\n\t\t\tresolve();\n\t\t}, ms);\n\t});\n}\n"]}