/* YouTube Focus Extension Styles */

.youtube-focus-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  font-family: '<PERSON><PERSON>', Arial, sans-serif;
}

.youtube-focus-modal-content {
  background: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.youtube-focus-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background: #f9f9f9;
  border-radius: 8px 8px 0 0;
}

.youtube-focus-modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
  font-weight: 500;
}

.youtube-focus-close-btn {
  background: none;
  border: none;
  font-size: 28px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.youtube-focus-close-btn:hover {
  background-color: #e0e0e0;
  color: #333;
}

.youtube-focus-modal-body {
  padding: 20px;
}

.youtube-focus-search-container {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

#youtube-focus-search {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 24px;
  font-size: 16px;
  outline: none;
  transition: border-color 0.2s;
}

#youtube-focus-search:focus {
  border-color: #ff0000;
}

#youtube-focus-search-btn {
  padding: 12px 24px;
  background-color: #ff0000;
  color: white;
  border: none;
  border-radius: 24px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

#youtube-focus-search-btn:hover {
  background-color: #cc0000;
}

.youtube-focus-results {
  min-height: 100px;
}

.youtube-focus-loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 16px;
}

.youtube-focus-error {
  text-align: center;
  padding: 40px;
  color: #ff0000;
  font-size: 16px;
}

/* Hide YouTube's default homepage content when modal is shown */
.youtube-focus-modal ~ #page-manager {
  filter: blur(5px);
  pointer-events: none;
}

/* Ensure video player remains visible and functional on video pages */
#movie_player {
  display: block !important;
}

/* Hide specific recommendation elements more aggressively */
ytd-compact-video-renderer,
ytd-video-secondary-info-renderer #meta-contents #container,
.ytd-watch-next-secondary-results-renderer,
#related,
#secondary-inner,
.ytp-endscreen-content,
.ytp-ce-element {
  display: none !important;
}

/* Keep essential video controls visible */
.ytp-chrome-controls,
.ytp-progress-bar-container,
.ytp-chrome-bottom {
  display: block !important;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .youtube-focus-modal-content {
    width: 95%;
    margin: 10px;
  }
  
  .youtube-focus-modal-header {
    padding: 15px;
  }
  
  .youtube-focus-modal-header h2 {
    font-size: 20px;
  }
  
  .youtube-focus-modal-body {
    padding: 15px;
  }
  
  .youtube-focus-search-container {
    flex-direction: column;
  }
  
  #youtube-focus-search-btn {
    align-self: flex-start;
  }
}
