{"name": "pathe", "version": "1.1.2", "description": "Universal filesystem path utils", "repository": "unjs/pathe", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./utils": {"types": "./dist/utils.d.ts", "import": "./dist/utils.mjs", "require": "./dist/utils.cjs"}}, "main": "./dist/index.cjs", "types": "./dist/index.d.ts", "files": ["dist", "utils.d.ts"], "devDependencies": {"@types/node": "^20.10.8", "@vitest/coverage-v8": "^1.1.3", "changelogen": "^0.5.5", "eslint": "^8.56.0", "eslint-config-unjs": "^0.2.1", "jiti": "^1.21.0", "prettier": "^3.1.1", "typescript": "^5.3.3", "unbuild": "^2.0.0", "vitest": "^1.1.3"}, "packageManager": "pnpm@8.14.0", "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint --ext .ts . && prettier -c src test", "lint:fix": "eslint --cache --ext .ts,.js,.mjs,.cjs . --fix && prettier -c src test -w", "release": "pnpm test && pnpm build && changelogen --release && pnpm publish && git push --follow-tags", "test": "pnpm lint && vitest run --coverage", "test:types": "tsc --noEmit"}}