{"name": "wxt", "type": "module", "version": "0.19.29", "description": "⚡ Next-gen Web Extension Framework", "repository": {"type": "git", "url": "git+https://github.com/wxt-dev/wxt.git"}, "homepage": "https://wxt.dev", "keywords": ["vite", "chrome", "web", "extension", "browser", "bundler", "framework"], "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "license": "MIT", "funding": "https://github.com/sponsors/wxt-dev", "files": ["bin", "dist"], "bin": {"wxt": "./bin/wxt.mjs", "wxt-publish-extension": "./bin/wxt-publish-extension.cjs"}, "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "./client": {"types": "./dist/client/index.d.ts", "default": "./dist/client/index.mjs"}, "./sandbox": {"types": "./dist/sandbox/index.d.ts", "default": "./dist/sandbox/index.mjs"}, "./browser": {"types": "./dist/browser/index.d.ts", "default": "./dist/browser/index.mjs"}, "./browser/chrome": {"types": "./dist/browser/chrome.d.ts", "import": "./dist/browser/chrome.mjs"}, "./testing": {"types": "./dist/testing/index.d.ts", "default": "./dist/testing/index.mjs"}, "./storage": {"types": "./dist/storage.d.ts", "default": "./dist/storage.mjs"}, "./vite-builder-env": {"types": "./dist/vite-builder-env.d.ts"}, "./modules": {"types": "./dist/modules.d.ts", "default": "./dist/modules.mjs"}}, "dependencies": {"@1natsu/wait-element": "^4.1.2", "@aklinker1/rollup-plugin-visualizer": "5.12.0", "@types/chrome": "^0.0.280", "@types/webextension-polyfill": "^0.12.1", "@webext-core/fake-browser": "^1.3.1", "@webext-core/isolated-element": "^1.1.2", "@webext-core/match-patterns": "^1.0.3", "@wxt-dev/storage": "^1.0.0", "async-mutex": "^0.5.0", "c12": "^3.0.2", "cac": "^6.7.14", "chokidar": "^4.0.3", "ci-info": "^4.1.0", "consola": "^3.2.3", "defu": "^6.1.4", "dotenv": "^16.4.5", "dotenv-expand": "^12.0.1", "esbuild": "^0.25.0", "fast-glob": "^3.3.2", "filesize": "^10.1.6", "fs-extra": "^11.2.0", "get-port-please": "^3.1.2", "giget": "^1.2.3", "hookable": "^5.5.3", "import-meta-resolve": "^4.1.0", "is-wsl": "^3.1.0", "jiti": "^2.4.2", "json5": "^2.2.3", "jszip": "^3.10.1", "linkedom": "^0.18.5", "magicast": "^0.3.5", "minimatch": "^10.0.1", "nano-spawn": "^0.2.0", "normalize-path": "^3.0.0", "nypm": "^0.3.12", "ohash": "^1.1.4", "open": "^10.1.0", "ora": "^8.1.1", "perfect-debounce": "^1.0.0", "picocolors": "^1.1.1", "prompts": "^2.4.2", "publish-browser-extension": "^2.3.0 || ^3.0.0", "scule": "^1.3.0", "unimport": "^3.13.1", "vite": "^5.0.0 || ^6.0.0", "vite-node": "^2.1.4 || ^3.0.0", "web-ext-run": "^0.2.1", "webextension-polyfill": "^0.12.0"}, "devDependencies": {"@aklinker1/check": "^1.4.5", "@faker-js/faker": "^9.2.0", "@types/fs-extra": "^11.0.4", "@types/lodash.merge": "^4.6.9", "@types/node": "^20.17.6", "@types/normalize-path": "^3.0.2", "@types/prompts": "^2.4.9", "extract-zip": "^2.0.1", "happy-dom": "^17.1.8", "lodash.merge": "^4.6.2", "oxlint": "^0.11.1", "publint": "^0.2.12", "typescript": "^5.6.3", "unbuild": "^3.5.0", "vitest": "^3.0.7", "vitest-plugin-random-seed": "^1.1.1"}, "peerDependenciesMeta": {"@types/chrome": {"optional": true}}, "scripts": {"wxt": "tsx src/cli/index.ts", "build": "buildc -- unbuild", "check": "pnpm build && pnpm run --reporter-hide-prefix /^check:.*/", "check:default": "check", "check:tsc-virtual": "tsc --noEmit -p src/virtual", "test": "buildc --deps-only -- vitest", "test:coverage": "pnpm test -- run --coverage", "sync-releases": "pnpx changelogen@latest gh release"}}