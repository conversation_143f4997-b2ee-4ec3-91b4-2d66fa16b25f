import { defineConfig } from "wxt";

export default defineConfig({
  extensionApi: "chrome",
  manifest: ({ mode }) => ({
    name: "YouTube Focus Extension",
    description:
      "A focused YouTube experience without distracting recommendations",
    version: "1.0.0",
    permissions: ["activeTab", "storage"],
    host_permissions: ["*://www.youtube.com/*", "*://youtube.com/*"],
    // Only include development features in dev mode
    ...(mode === "development"
      ? {
          // Development-only features can go here
        }
      : {
          // Production-only features
        }),
  }),
});
