import { VIRTUAL_NOOP_BACKGROUND_MODULE_ID } from "../../../utils/constants.mjs";
export function noopBackground() {
  const virtualModuleId = VIRTUAL_NOOP_BACKGROUND_MODULE_ID;
  const resolvedVirtualModuleId = "\0" + virtualModuleId;
  return {
    name: "wxt:noop-background",
    resolveId(id) {
      if (id === virtualModuleId) return resolvedVirtualModuleId;
    },
    load(id) {
      if (id === resolvedVirtualModuleId) {
        return `import { defineBackground } from 'wxt/sandbox';
export default defineBackground(() => void 0)`;
      }
    }
  };
}
