/** @license MIT License (c) copyright B Cavalier & <PERSON> */

/**
 * cancelable.js
 * @deprecated
 *
 * Decorator that makes a deferred "cancelable".  It adds a cancel() method that
 * will call a special cancel handler function and then reject the deferred.  The
 * cancel handler can be used to do resource cleanup, or anything else that should
 * be done before any other rejection handlers are executed.
 *
 * Usage:
 *
 * var cancelableDeferred = cancelable(when.defer(), myCancelHandler);
 *
 * <AUTHOR>
 */

(function(define) {
define(function() {

    /**
     * Makes deferred cancelable, adding a cancel() method.
	 * @deprecated
     *
     * @param deferred {Deferred} the {@link Deferred} to make cancelable
     * @param canceler {Function} cancel handler function to execute when this deferred
	 * is canceled.  This is guaranteed to run before all other rejection handlers.
	 * The canceler will NOT be executed if the deferred is rejected in the standard
	 * way, i.e. deferred.reject().  It ONLY executes if the deferred is canceled,
	 * i.e. deferred.cancel()
     *
     * @returns deferred, with an added cancel() method.
     */
    return function(deferred, canceler) {
        // Add a cancel method to the deferred to reject the delegate
        // with the special canceled indicator.
        deferred.cancel = function() {
			try {
				deferred.reject(canceler(deferred));
			} catch(e) {
				deferred.reject(e);
			}

			return deferred.promise;
        };

        return deferred;
    };

});
})(typeof define === 'function' && define.amd ? define : function (factory) { module.exports = factory(); });


