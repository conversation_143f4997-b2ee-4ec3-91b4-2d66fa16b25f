<!-- DO NOT EDIT, THIS FILE WAS GENERATED BY '../../scripts/generate-readmes.sh' -->
<h1 align="center">
  <img align="top" width="44" src="https://raw.githubusercontent.com/wxt-dev/wxt/HEAD/docs/public/hero-logo.svg" alt="WXT Logo">
  <span>WXT</span>
</h1>

<p align="center">
  <a href="https://www.npmjs.com/package/wxt" target="_blank"><img alt="npm version" src="https://img.shields.io/npm/v/wxt?labelColor=black&color=%234fa048"></a>
  <span> </span>
  <a href="https://www.npmjs.com/package/wxt" target="_blank"><img alt="downloads" src="https://img.shields.io/npm/dm/wxt?labelColor=black&color=%234fa048"></a>
  <span> </span>
  <a href="https://github.com/wxt-dev/wxt/blob/main/LICENSE" target="_blank"><img alt="license | MIT" src="https://img.shields.io/npm/l/wxt?labelColor=black&color=%234fa048"></a>
  <span> </span>
  <a href="https://codecov.io/github/wxt-dev/wxt" target="_blank"><img alt="coverage" src="https://img.shields.io/codecov/c/github/wxt-dev/wxt?labelColor=black&color=%234fa048"></a>
</p>

<p align="center">
  <span>Next-gen framework for developing web extensions.</span>
  <br/>
  <span>⚡</span>
  <br/>
  <q><i>It's like Nuxt, but for Web Extensions</i></q>
</p>

<p align="center">
  <a href="https://wxt.dev/guide/installation.html" target="_blank">Get Started</a>
  &bull;
  <a href="https://wxt.dev/api/config.html" target="_blank">Configuration</a>
  &bull;
  <a href="https://wxt.dev/examples.html" target="_blank">Examples</a>
  &bull;
  <a href="https://github.com/wxt-dev/wxt/blob/main/packages/wxt/CHANGELOG.md" target="_blank">Changelog</a>
  &bull;
  <a href="https://discord.gg/ZFsZqGery9" target="_blank">Discord</a>
</p>

![Example CLI Output](https://raw.githubusercontent.com/wxt-dev/wxt/HEAD/docs/assets/cli-output.png)

## Demo

https://github.com/wxt-dev/wxt/assets/10101283/4d678939-1bdb-495c-9c36-3aa281d84c94

## Quick Start

Bootstrap a new project:

<!-- automd:pm-x version="latest" name="wxt" args="init" -->

```sh
# npm
npx wxt@latest init

# pnpm
pnpm dlx wxt@latest init

# bun
bunx wxt@latest init
```

<!-- /automd -->

Or see the [installation guide](https://wxt.dev/guide/installation.html) to get started with WXT.

## Features

- 🌐 Supports all browsers
- ✅ Supports both MV2 and MV3
- ⚡ Dev mode with HMR & fast reload
- 📂 File based entrypoints
- 🚔 TypeScript
- 🦾 Auto-imports
- 🤖 Automated publishing
- 🎨 Frontend framework agnostic: works with Vue, React, Svelte, etc
- 📦 [Module system](https://wxt.dev/guide/essentials/wxt-modules.html#overview) for reusing code between extensions
- 🖍️ Quickly bootstrap a new project
- 📏 Bundle analysis
- ⬇️ Download and bundle remote URL imports

## Sponsors

WXT is a [MIT-licensed](https://github.com/wxt-dev/wxt/blob/main/LICENSE) open source project with its ongoing development made possible entirely by the support of these awesome backers. If you'd like to join them, please consider [sponsoring WXT's development](https://github.com/sponsors/wxt-dev).

<a href="https://github.com/sponsors/wxt-dev"><img alt="WXT Sponsors" src="https://raw.githubusercontent.com/wxt-dev/static/refs/heads/main/sponsorkit/sponsors.svg"></a>

## Contributors

<!-- automd:contributors author="aklinker1" license="MIT" github="wxt-dev/wxt" -->

Published under the [MIT](https://github.com/wxt-dev/wxt/blob/main/LICENSE) license.
Made by [@aklinker1](https://github.com/aklinker1) and [community](https://github.com/wxt-dev/wxt/graphs/contributors) 💛
<br><br>
<a href="https://github.com/wxt-dev/wxt/graphs/contributors">
<img src="https://contrib.rocks/image?repo=wxt-dev/wxt" />
</a>

<!-- /automd -->
