import { config } from "dotenv";
import { expand } from "dotenv-expand";
export function loadEnv(mode, browser) {
  return expand(
    config({
      // Files on top override files below
      path: [
        `.env.${mode}.${browser}.local`,
        `.env.${mode}.${browser}`,
        `.env.${browser}.local`,
        `.env.${browser}`,
        `.env.${mode}.local`,
        `.env.${mode}`,
        `.env.local`,
        `.env`
      ]
    })
  );
}
