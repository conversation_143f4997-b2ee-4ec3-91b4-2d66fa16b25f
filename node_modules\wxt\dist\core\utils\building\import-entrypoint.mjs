import createJITI from "jiti";
import { createUnimport } from "unimport";
import fs from "fs-extra";
import { relative, resolve } from "node:path";
import { removeProjectImportStatements } from "../../utils/strings.mjs";
import { normalizePath } from "../../utils/paths.mjs";
import { transformSync } from "esbuild";
import { fileURLToPath } from "node:url";
import { wxt } from "../../wxt.mjs";
export async function importEntrypointFile(path) {
  wxt.logger.debug("Loading file metadata:", path);
  const normalPath = normalizePath(path);
  const unimport = createUnimport({
    ...wxt.config.imports,
    // Only allow specific imports, not all from the project
    dirs: []
  });
  await unimport.init();
  const text = await fs.readFile(path, "utf-8");
  const textNoImports = removeProjectImportStatements(text);
  const { code } = await unimport.injectImports(textNoImports);
  wxt.logger.debug(
    ["Text:", text, "No imports:", textNoImports, "Code:", code].join("\n")
  );
  const jiti = createJITI(
    typeof __filename !== "undefined" ? __filename : fileURLToPath(import.meta.url),
    {
      cache: false,
      debug: wxt.config.debug,
      alias: {
        "webextension-polyfill": resolve(
          wxt.config.wxtModuleDir,
          "dist/virtual/mock-browser.mjs"
        ),
        // TODO: Resolve this virtual module to some file with
        // `export default {}` instead of this hack of using another file with
        // a default export.
        "virtual:app-config": resolve(
          wxt.config.wxtModuleDir,
          "dist/virtual/mock-browser.mjs"
        )
      },
      // Continue using node to load TS files even if `bun run --bun` is detected. Jiti does not
      // respect the custom transform function when using it's native bun option.
      tryNative: false,
      // List of extensions to transform with esbuild
      extensions: [
        ".ts",
        ".cts",
        ".mts",
        ".tsx",
        ".js",
        ".cjs",
        ".mjs",
        ".jsx"
      ],
      transform(opts) {
        const isEntrypoint = opts.filename === normalPath;
        return transformSync(
          // Use modified source code for entrypoints
          isEntrypoint ? code : opts.source,
          getEsbuildOptions(opts)
        );
      }
    }
  );
  try {
    const res = await jiti(path);
    return res.default;
  } catch (err) {
    const filePath = relative(wxt.config.root, path);
    if (err instanceof ReferenceError) {
      const variableName = err.message.replace(" is not defined", "");
      throw Error(
        `${filePath}: Cannot use imported variable "${variableName}" outside the main function. See https://wxt.dev/guide/go-further/entrypoint-side-effects.html`,
        { cause: err }
      );
    } else {
      wxt.logger.error(err);
      throw Error(`Failed to load entrypoint: ${filePath}`, { cause: err });
    }
  }
}
function getEsbuildOptions(opts) {
  const isJsx = opts.filename?.endsWith("x");
  return {
    format: "cjs",
    loader: isJsx ? "tsx" : "ts",
    define: {
      "import.meta.env.ENTRYPOINT": '"build"'
    },
    ...isJsx ? {
      // `h` and `Fragment` are undefined, but that's OK because JSX is never evaluated while
      // grabbing the entrypoint's options.
      jsxFactory: "h",
      jsxFragment: "Fragment"
    } : void 0
  };
}
